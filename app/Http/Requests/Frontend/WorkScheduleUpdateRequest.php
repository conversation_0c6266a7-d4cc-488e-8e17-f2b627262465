<?php

namespace App\Http\Requests\Frontend;

use App\Enums\Day;
use App\Enums\WorkAndOffDaysDistributionType;
use App\Enums\WorkScheduleType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class WorkScheduleUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        $workSchedule = $this->route('workSchedule');
        $type = $this->enum('type', WorkScheduleType::class);
        $workAndOffDaysType = $this->enum(
            'work_and_off_days_distribution_type',
            WorkAndOffDaysDistributionType::class
        );

        return [
            'type' => ['required', Rule::enum(WorkScheduleType::class)],
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::uniqueTenant('work_schedules', 'name')->ignore($workSchedule?->id),
            ],
            'work_and_off_days_distribution_type' => [
                'required',
                Rule::enum(WorkAndOffDaysDistributionType::class),
            ],
            'off_days_after_each_repetition' => [
                Rule::requiredIf(
                    fn() => $type?->isRotational() && $workAndOffDaysType?->isNumberOfDays()
                ),
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'start_date' => [
                Rule::requiredIf(fn() => $type?->isRotational()),
                'nullable',
                'date',
            ],
            'workdays' => [
                'required',
                'array',
                'min:1',
                fn($attribute, $value, $fail) => match (true) {
                    $type->isFixed() && count($value) !== 1 => $fail(
                        __('Fixed work schedules must have exactly one workday.')
                    ),
                    $type->isRotational() && count($value) < 2 => $fail(
                        __('Rotational work schedules must have at least two workdays.')
                    ),
                    default => null,
                },
            ],
            'workdays.*.workday_id' => [
                'required',
                'integer',
                Rule::existsTenant('workdays', 'id'),
            ],
            'workdays.*.selected_specific_days' => [
                Rule::requiredIf(fn() => $workAndOffDaysType?->isSpecificDays()),
                Rule::prohibitedIf(fn() => $workAndOffDaysType?->isNumberOfDays()),
                'nullable',
                'array',
                'min:1',
            ],
            'workdays.*.selected_specific_days.*' => ['string', Rule::enum(Day::class)],
            'workdays.*.work_days_number' => [
                Rule::requiredIf(fn() => $workAndOffDaysType?->isNumberOfDays()),
                Rule::prohibitedIf(fn() => $workAndOffDaysType?->isSpecificDays()),
                'nullable',
                'integer',
                'min:1',
                'max:30',
            ],
            'workdays.*.off_days_number' => [
                Rule::requiredIf(fn() => $workAndOffDaysType?->isNumberOfDays()),
                Rule::prohibitedIf(fn() => $workAndOffDaysType?->isSpecificDays()),
                'nullable',
                'integer',
                'min:0',
                'max:30',
            ],
            'workdays.*.repetitions_number' => [
                Rule::requiredIf(
                    fn() => $type->isRotational() && $workAndOffDaysType->isNumberOfDays()
                ),
                Rule::prohibitedIf(
                    fn() => $type->isFixed() || $workAndOffDaysType->isSpecificDays()
                ),
                'nullable',
                'integer',
                'min:1',
                'max:52',
            ],
            'assignments' => ['nullable', 'array'],
            'assignments.employees' => ['nullable', 'array'],
            'assignments.employees.*' => ['integer', Rule::existsTenant('employees', 'id')],
            'assignments.departments' => ['nullable', 'array'],
            'assignments.departments.*' => ['integer', Rule::existsTenant('departments', 'id')],
            'assignments.direct_managers' => ['nullable', 'array'],
            'assignments.direct_managers.*' => ['integer', Rule::existsTenant('employees', 'id')],
            'assignments.tags' => ['nullable', 'array'],
            'assignments.tags.*' => ['integer', Rule::existsTenant('tags', 'id')],
            'assignments.locations' => ['nullable', 'array'],
            'assignments.locations.*' => ['integer', Rule::existsTenant('locations', 'id')],
        ];
    }

    public function messages(): array
    {
        return [
            'workdays.required' => __('At least one workday must be selected.'),
            'workdays.min' => __('At least one workday must be selected.'),
            'workdays.*.selected_specific_days.required_if' => __(
                'Specific days must be selected when using specific days distribution.'
            ),
            'workdays.*.selected_specific_days.prohibited_if' => __(
                'Specific days cannot be used with number of days distribution.'
            ),
            'workdays.*.selected_specific_days.min' => __('At least one day must be selected.'),
            'off_days_after_each_repetition.required_if' => __(
                'Off days after each repetition is required for rotational schedules with number of days distribution.'
            ),
            'start_date.required_if' => __(
                'Start date is required for rotational work schedules.'
            ),
            'workdays.*.work_days_number.required_if' => __(
                'Work days number is required when using number of days distribution.'
            ),
            'workdays.*.work_days_number.prohibited_if' => __(
                'Work days number cannot be used with specific days distribution.'
            ),
            'workdays.*.off_days_number.required_if' => __(
                'Off days number is required when using number of days distribution.'
            ),
            'workdays.*.off_days_number.prohibited_if' => __(
                'Off days number cannot be used with specific days distribution.'
            ),
            'workdays.*.repetitions_number.required_if' => __(
                'Repetitions number is required for rotational schedules with number of days distribution.'
            ),
            'workdays.*.repetitions_number.prohibited_if' => __(
                'Repetitions number cannot be used with fixed schedules or specific days distribution.'
            ),
        ];
    }
}
