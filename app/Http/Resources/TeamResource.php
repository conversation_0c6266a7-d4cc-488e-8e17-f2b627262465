<?php

namespace App\Http\Resources;

use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Team */
class TeamResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'primary_color' => $this->primary_color,
            'active' => $this->active,
            'remote_work' => $this->remote_work,
            'free_checkout' => $this->free_checkout,
            'employees_weekly_summary' => $this->employees_weekly_summary,
            'approval_requests_limit' => $this->approval_requests_limit,
            'leave_request' => $this->leave_request,
            'approval_request' => $this->approval_request,
            'permission_request' => $this->permission_request,
            'permission_request_daily_limit_hours' => $this->permission_request_daily_limit_hours,
            'permission_request_monthly_limit_hours' =>
                $this->permission_request_monthly_limit_hours,
            'remote_work_days_yearly_limit' => $this->remote_work_days_yearly_limit,
            'remote_work_days_monthly_limit' => $this->remote_work_days_monthly_limit,
            'remote_work_days_weekly_limit' => $this->remote_work_days_weekly_limit,
            'vacation_weekend' => $this->vacation_weekend,
            'checkout_reminder_config' => $this->checkout_reminder_config,
            'map_report_thresholds' => $this->map_report_thresholds,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'early_late_config' => $this->early_late_config->toArrayWithTagsLoaded(),
            'random_proof_notification_config' => $this->random_proof_notification_config,
            'employee_statement_config' => $this->employee_statement_config,
            'approval_type' => $this->approval_type,
            'new_work_schedule_feature_enabled' => $this->new_work_schedule_feature_enabled,
        ];
    }
}
