<?php

namespace App\Http\Resources\Frontend;

use App\Models\Workday;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Workday */
class WorkdayResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'start_time' => $this->start_time->format('H:i:s'),
            'end_time' => $this->end_time->format('H:i:s'),
            'start_time_including_flexible_time' => $this->start_time_including_flexible_time->format(
                'H:i:s'
            ),
            'end_time_including_flexible_time' => $this->end_time_including_flexible_time->format(
                'H:i:s'
            ),
            'color' => $this->color,
            'flexible_time_before' => $this->flexible_time_before->format('H:i:s'),
            'flexible_time_after' => $this->flexible_time_after->format('H:i:s'),
            'prevent_checkout_after' => $this->prevent_checkout_after->format('H:i:s'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // Work schedule pivot data (when workday is loaded through work schedule relationship)
            'work_schedule_data' => $this->when(
                $this->pivot,
                fn() => [
                    'selected_specific_days' => $this->pivot->selected_specific_days,
                    'work_days_number' => $this->pivot->work_days_number,
                    'off_days_number' => $this->pivot->off_days_number,
                    'repetitions_number' => $this->pivot->repetitions_number,
                ]
            ),
        ];
    }
}
