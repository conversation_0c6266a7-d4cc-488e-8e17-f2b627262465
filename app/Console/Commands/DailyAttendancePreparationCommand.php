<?php

namespace App\Console\Commands;

use App\Jobs\PrepareEmployeeAttendanceRecord;
use App\Models\Employee;
use Carbon\CarbonImmutable;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;

class DailyAttendancePreparationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:prepare {date? : date to prepare, example YYYY-MM-DD}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'prepare attendance records for all employees';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $validator = Validator::make(
            [
                'date' => $this->argument('date'),
            ],
            [
                'date' => ['nullable', 'date', 'date_format:Y-m-d', 'before_or_equal:today'],
            ]
        );
        if ($validator->fails()) {
            $this->info('Validation failed. See error messages below:');

            foreach ($validator->errors()->all() as $error) {
                $this->error($error);
            }
            return 1;
        }

        $date = $this->argument('date')
            ? CarbonImmutable::createFromFormat('Y-m-d', $this->argument('date'))
            : null;

        Employee::query()
            ->belongToActiveTeam()
            ->active()
            ->withWhereHas('shift')
            ->each(fn($employee) => PrepareEmployeeAttendanceRecord::dispatch($employee, $date));

        return 0;
    }
}
