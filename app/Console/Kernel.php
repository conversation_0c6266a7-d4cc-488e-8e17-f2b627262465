<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

/**
 * @codeCoverageIgnore
 */
class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // 01:00 so it does not interfere with other attendance prepare
        $schedule->command('audit:prune')->dailyAt('01:00')->when(config('audit.enabled'));

        $schedule->command('old-notifications:prune')->daily();

        $schedule->command('model:prune')->daily();

        $schedule->command('streamer:failed:retry --all')->everyFiveMinutes()->withoutOverlapping();

        $schedule->command('attendance:prepare')->daily();
        $schedule->command('work-schedule:generate-records')->daily()->at('02:00');
        $schedule
            ->command('attendance:register-absence')
            ->everyFifteenMinutes()
            ->withoutOverlapping();
        $schedule
            ->command('attendance:checkin-reminder')
            ->everyFifteenMinutes()
            ->withoutOverlapping();
        $schedule->command('attendance:expired-proofs')->everyFiveMinutes()->withoutOverlapping();
        $schedule->command('attendance:weekly-summary')->weekly()->environments('production');
        $schedule->command('attendance:notify-managers')->dailyAt('13:00');

        $schedule->command('reports:email-early-late-report-weekly')->weekly();

        $schedule->command('reports:email-early-late-report-monthly')->monthly();

        $schedule->command('webhook:proccess')->everyFiveMinutes()->withoutOverlapping();

        $schedule->command('leaves:sync-to-attendance')->hourly()->withoutOverlapping();

        $schedule->command('app:token-expiration-reminder')->weekly();
        $schedule->command('sanctum:prune-expired --hours=24')->daily();

        $schedule->command('clear:old-reports')->daily();

        $schedule->command('clear:temp-files')->daily();

        $schedule->command('queue:prune-failed')->daily();

        $schedule->command('report-tasks:mark-stale-as-failed')->hourly();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load([__DIR__ . '/Commands']);

        require base_path('routes/console.php');
    }
}
