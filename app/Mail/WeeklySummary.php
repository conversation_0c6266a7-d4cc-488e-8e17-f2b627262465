<?php

namespace App\Mail;

use App\Models\Attendance;
use App\Models\Employee;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Mail\Mailable;

class WeeklySummary extends Mailable implements ShouldQueue
{
    use Queueable;

    private $colors = [
        '#82f6cc',
        '#79eac0',
        '#70deb5',
        '#67d2a9',
        '#5dc79e',
        '#54bb93',
        '#4bb088',
        '#42a57d',
        '#399972',
        '#308e68',
        '#26845e',
        '#1c7953',
        '#106e4a',
        '#016440',
    ];

    private CarbonPeriod $period;

    public function __construct(private Employee $employee)
    {
        $periodStart = now()
            ->subWeek()
            ->startOfWeek(Carbon::SUNDAY);

        $periodEnd = now()
            ->subWeek()
            ->endOfWeek(Carbon::SATURDAY);

        $this->period = CarbonPeriod::create($periodStart, $periodEnd);
    }

    public function build(): WeeklySummary
    {
        $attendances = $this->fetchAttendancesOfThreeMonths();

        $graph = $this->prepareAttendanceGraph($attendances);

        $days = $graph['days'];
        $presentCount = $graph['presentCount'];
        $absentCount = $graph['absentCount'];
        $thisWeekSeconds = $graph['thisWeekSeconds'];
        $lastWeekSeconds = $this->getPreviousWeekSeconds($attendances);

        $averageThisWeek = $thisWeekSeconds
            ? CarbonInterval::seconds(array_sum($thisWeekSeconds) / count($thisWeekSeconds))
            : CarbonInterval::seconds(0);
        $averageLastWeek = $lastWeekSeconds
            ? CarbonInterval::seconds(array_sum($lastWeekSeconds) / count($lastWeekSeconds))
            : CarbonInterval::seconds(0);

        $percentChange = $averageThisWeek->total('seconds')
            ? round(
                (1 - $averageLastWeek->total('seconds') / $averageThisWeek->total('seconds')) * 100,
                2
            )
            : 0;

        $averageHours = round($averageThisWeek->total('hours'), 1);

        $dayLabels = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

        $calendar = [];
        $month = $this->period->end->copy()->subMonths(2);

        for ($i = 0; $i < 3; $i++) {
            $calendar[] = $this->prepareCalendar($month, $attendances);
            $month->addMonth();
        }

        $employee = $this->employee;

        return $this->view('emails.employees.weekly_summary', [
            'employee' => $employee,
            'periodStart' => $this->period->start,
            'periodEnd' => $this->period->end,
            'days' => $days,
            'dayLabels' => $dayLabels,
            'calendar' => $calendar,
            'presentCount' => $presentCount,
            'absentCount' => $absentCount,
            'averageHours' => $averageHours,
            'percentChange' => $percentChange,
        ]);
    }

    private function prepareAttendanceGraph($attendances): array
    {
        $days = [];
        $presentCount = 0;
        $absentCount = 0;
        $thisWeekSeconds = [];

        $startDate = $this->period->start;
        $endDate = $this->period->end;

        while ($startDate->lessThan($endDate)) {
            $row = $attendances
                ->where('date', $startDate)
                ->whereIn('status', [Attendance::PRESENT, Attendance::ABSENT])
                ->first();

            if (isset($row)) {
                $days[] = [
                    'label' => $startDate->shortEnglishDayOfWeek,
                    'longLabel' => $startDate->englishDayOfWeek,
                    'row' => $row,
                    'present' =>
                        $row->status === Attendance::PRESENT
                            ? ($row->actual_hours_pct / 100) * 50
                            : 0,
                    'absent' => $row->status === Attendance::ABSENT ? 50 : 0,
                ];

                if ($row->status === Attendance::PRESENT) {
                    $presentCount++;
                    $thisWeekSeconds[] = $row->actual_hours_in_sec;
                } else {
                    $absentCount++;
                }
            }

            $startDate->addDay();
        }

        return [
            'days' => $days,
            'presentCount' => $presentCount,
            'absentCount' => $absentCount,
            'thisWeekSeconds' => $thisWeekSeconds,
        ];
    }

    private function getPreviousWeekSeconds($attendances): array
    {
        $previousWeekStart = $this->period->start->subWeeks(2)->startOfWeek(Carbon::SUNDAY);
        $previousWeekEnd = $previousWeekStart->copy()->endOfWeek(Carbon::SATURDAY);
        $lastWeekSeconds = [];

        while ($previousWeekStart->lessThan($previousWeekEnd)) {
            $row = $attendances
                ->where('date', $previousWeekStart)
                ->whereIn('status', [Attendance::PRESENT])
                ->first();

            if ($row) {
                $lastWeekSeconds[] = $row->actual_hours_in_sec;
            }

            $previousWeekStart->addDay();
        }

        return $lastWeekSeconds;
    }

    private function prepareCalendar(CarbonInterface $month, $attendances): array
    {
        $startOfCalendar = $month
            ->copy()
            ->firstOfMonth()
            ->startOfWeek(Carbon::SUNDAY);
        $endOfCalendar = $month
            ->copy()
            ->lastOfMonth()
            ->endOfWeek(Carbon::SATURDAY);
        $mDays = [];

        while ($startOfCalendar <= $endOfCalendar) {
            if ($startOfCalendar->format('m') != $month->format('m')) {
                $mDays[] = -1;
            } else {
                $row = $attendances->where('date', $startOfCalendar)->first();
                $label = $startOfCalendar->format('M jS, Y');
                $color = '#eee';
                $value = 'None';
                if ($row) {
                    if ($row->status === Attendance::PRESENT) {
                        $number = round(($row->actual_hours_pct / 100) * 10) - 1;
                        if ($number > 13) {
                            $color = $this->colors[13];
                        } elseif ($number < 0) {
                            $color = $this->colors[0];
                        } else {
                            $color = $this->colors[$number];
                        }
                        $value = $row->out_type ? $row->net_hours->format('H:i') : '-';
                    } elseif ($row->status === Attendance::WEEKEND) {
                        $color = '#ccc';
                        $value = 'Weekend';
                    } else {
                        $color = '#EF4444';
                        $value = 'ABSENT';
                    }
                }

                $mDays[] = [
                    'label' => $label,
                    'color' => $color,
                    'value' => $value,
                ];
            }
            $startOfCalendar->addDay();
        }

        return [
            'label' => $month->format('M Y'),
            'days' => $mDays,
        ];
    }

    public function fetchAttendancesOfThreeMonths(): Collection
    {
        $startDate = $this->period->start->copy()->subMonth()->startOfMonth();
        $endDate = $this->period->end->copy()->addMonth()->endOfMonth();

        return $this->employee
            ->attendances()
            ->whereBetween('date', [$startDate, $endDate])
            ->get();
    }
}
