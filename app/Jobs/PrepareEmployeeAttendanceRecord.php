<?php

namespace App\Jobs;

use App\Enums\CheckoutReminderConfig;
use App\Models\Attendance;
use App\Models\Employee;
use App\Notifications\CheckoutReminderNotification;
use App\Services\PrepareAttendanceUsingNewWorkScheduleFeatureService;
use App\Services\PrepareAttendanceUsingOldShiftFeatureService;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PrepareEmployeeAttendanceRecord implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private readonly CarbonImmutable $date;

    /**
     * @throws Exception date should not be in future
     */
    public function __construct(private readonly Employee $employee, ?CarbonImmutable $date = null)
    {
        if ($date && $date->isFuture()) {
            throw new Exception('date should not be in future');
        }

        $this->date = $date ?: CarbonImmutable::today();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $attendance = $this->employee->team->new_work_schedule_feature_enabled
            ? (new PrepareAttendanceUsingNewWorkScheduleFeatureService(
                employee: $this->employee,
                date: $this->date
            ))->handle()
            : (new PrepareAttendanceUsingOldShiftFeatureService(
                $this->employee,
                $this->date
            ))->handle();

        if (!$attendance || $attendance->status !== Attendance::YET) {
            return;
        }

        // Schedule checkout reminder
        if (
            $this->employee->team->checkout_reminder_config === CheckoutReminderConfig::ByShiftEnd
        ) {
            $this->employee->notify(
                (new CheckoutReminderNotification())->delay($attendance->shift_to->subMinutes(15))
            );
        }

        // Send employee statement notification to be sent if applicable
        if ($attendance->employee_statement_enabled) {
            SendEmployeeStatementIfApplicableJob::dispatch($attendance)->delay(
                $attendance->shift_to
            );
        }
    }
}
