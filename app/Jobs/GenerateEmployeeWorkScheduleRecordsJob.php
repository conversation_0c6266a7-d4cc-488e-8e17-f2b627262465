<?php

namespace App\Jobs;

use App\Enums\WorkdayType;
use App\Enums\WorkScheduleType;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleRecord;
use App\Support\WorkScheduleRotationState;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class GenerateEmployeeWorkScheduleRecordsJob implements ShouldQueue
{
    use Queueable;

    private Collection $holidays;
    private Collection $leaves;

    public function __construct(
        private readonly WorkSchedule $workSchedule,
        private readonly Employee $employee
    ) {
        $this->onQueue('work-schedule-records');
    }

    public function handle(): void
    {
        $this->workSchedule->load(['workdays', 'team']);

        $startDate = Carbon::parse($this->workSchedule->start_date)->max(Carbon::today());

        $endDate = $startDate->copy()->addMonths(6);

        $this->preloadHolidaysAndLeaves($startDate, $endDate);

        WorkScheduleRecord::where('work_schedule_id', $this->workSchedule->id)
            ->where('employee_id', $this->employee->id)
            ->where('date', '>=', $startDate->toDateString())
            ->doesntHave('attendances')
            ->delete();

        if ($this->workSchedule->type === WorkScheduleType::Fixed) {
            $this->generateFixedScheduleRecords($startDate, $endDate);
        } else {
            $this->generateRotationalScheduleRecords($startDate, $endDate);
        }
    }

    private function preloadHolidaysAndLeaves(Carbon $startDate, Carbon $endDate): void
    {
        $this->holidays = Holiday::query()
            ->where('team_id', $this->employee->team_id)
            ->date(from: $startDate, to: $endDate)
            ->get();

        $this->leaves = $this->employee
            ->leaves()
            ->approved()
            ->date(from: $startDate, to: $endDate)
            ->get();
    }

    private function generateFixedScheduleRecords(Carbon $startDate, Carbon $endDate): void
    {
        $workday = $this->workSchedule->workdays->first();

        if (!$workday) {
            return;
        }

        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $this->createRecord($currentDate, $workday);
            $currentDate->addDay();
        }
    }

    private function generateRotationalScheduleRecords(Carbon $startDate, Carbon $endDate): void
    {
        $workdays = $this->workSchedule->workdays;

        if ($workdays->isEmpty()) {
            return;
        }

        $currentDate = $startDate->copy();
        $rotationState = new WorkScheduleRotationState($workdays);

        while ($currentDate->lte($endDate)) {
            $currentWorkday = $rotationState->getCurrentWorkday();
            $this->createRecord($currentDate, $currentWorkday);

            $rotationState->advanceDay($this->workSchedule);

            $currentDate->addDay();
        }
    }

    private function createRecord(Carbon $date, $workday): void
    {
        $workdayType = $this->determineWorkdayType($date, $workday);

        WorkScheduleRecord::create([
            'team_id' => $this->workSchedule->team_id,
            'work_schedule_id' => $this->workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday->id,
            'date' => $date->toDateString(),
            'workday_type' => $workdayType,
        ]);
    }

    private function determineWorkdayType(Carbon $date, $workday): WorkdayType
    {
        $isHoliday = $this->holidays->contains(
            fn($holiday) => $date->between($holiday->start_date, $holiday->end_date)
        );

        $onLeave = $this->leaves->contains(
            fn($leave) => $date->between($leave->from_date, $leave->to_date)
        );

        $isWeekend = $this->isWeekendDay($date, $workday);

        $vacationWeekendPolicy = $this->workSchedule->team->vacation_weekend;

        if ($onLeave) {
            return $isWeekend && !$vacationWeekendPolicy
                ? WorkdayType::Weekend
                : WorkdayType::Leave;
        }

        if ($isHoliday) {
            return WorkdayType::Holiday;
        }

        return $isWeekend ? WorkdayType::Weekend : WorkdayType::Weekday;
    }

    private function isWeekendDay(Carbon $date, $workday): bool
    {
        if ($this->workSchedule->work_and_off_days_distribution_type->isSpecificDays()) {
            // If the day is NOT in the selected_specific_days array, it's considered a weekend
            return !in_array(
                strtolower($date->format('l')),
                $workday->pivot->selected_specific_days ?? []
            );
        }

        // For number of days distribution, weekend determination is handled by the rotation logic
        // This method is called after rotation logic determines work/off periods
        return false;
    }
}
