<?php

namespace App\Jobs;

use App\Models\Employee;
use App\Models\WorkSchedule;
use App\Services\ResolveWorkScheduleEmployeesService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class GenerateWorkScheduleRecordsJob implements ShouldQueue
{
    use Queueable;

    public function __construct(private readonly WorkSchedule $workSchedule)
    {
        $this->onQueue('work-schedule-records');
    }

    public function handle(): void
    {
        $this->workSchedule->loadMissing('assignments');

        ResolveWorkScheduleEmployeesService::handle($this->workSchedule->assignments)->each(
            fn(Employee $employee) => GenerateEmployeeWorkScheduleRecordsJob::dispatch(
                $this->workSchedule,
                $employee
            )
        );
    }
}
