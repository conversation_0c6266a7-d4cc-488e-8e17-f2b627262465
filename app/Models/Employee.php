<?php

namespace App\Models;

use App\Calculations\EmployeeBalanceCalculator;
use App\DTOs\RandomProofNotificationConfig;
use App\Jobs\PrepareEmployeeAttendanceRecord;
use App\Traits\BelongsToTenant;
use App\Traits\HasDelegations;
use App\Traits\HasNawartUuid;
use Arr;
use Carbon\CarbonPeriod;
use Illuminate\Contracts\Translation\HasLocalePreference;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;
use Znck\Eloquent\Traits\BelongsToThrough;
use function blank;
use function collect;

class Employee extends Authenticatable implements AuditableContract, HasLocalePreference
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;
    use HasNawartUuid;
    use Notifiable;
    use HasDelegations;
    use BelongsToThrough;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'team_id',
        'department_id',
        'manager_id',
        'shift_id',
        'first_name',
        'last_name',
        'mobile',
        'position',
        'remote_work',
        'preferred_language',
        'device_id',
        'number',
        'is_active',
        'nawart_uuid',
        'email',
        'last_activity_at',
        'first_login_at',
        'app_version',
        'device_os',
        'device_name',
        'roles',
        'random_proof_notification_config',
        'is_ready',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'department_id' => 'integer',
        'is_active' => 'boolean',
        'random_proof_notification_config' => RandomProofNotificationConfig::class,
        'last_activity_at' => 'datetime',
        'first_login_at' => 'datetime',
        'roles' => 'collection',
        'is_ready' => 'boolean',
    ];

    public $webhookPayload = ['id', 'name', 'email', 'department_id', 'department_name'];

    public function directManager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'manager_id');
    }

    public function scopeDirectlyManaged(Builder $query): Builder
    {
        return $query->whereNotNull('manager_id');
    }

    public function scopeManagedByDepartment(Builder $query): Builder
    {
        return $query->whereNull('manager_id');
    }

    public function departmentManager(): \Znck\Eloquent\Relations\BelongsToThrough
    {
        return $this->belongsToThrough(
            related: Employee::class,
            through: Department::class,
            foreignKeyLookup: [
                Employee::class => 'manager_id',
            ]
        );
    }

    public function directlyManagedEmployees(): HasMany
    {
        return $this->hasMany(Employee::class, 'manager_id');
    }

    public function directlyManagedEmployeesLeaves(): HasManyThrough
    {
        return $this->hasManyThrough(Leave::class, Employee::class, 'manager_id');
    }

    public function directlyManagedEmployeesApprovalRequests(): HasManyThrough
    {
        return $this->hasManyThrough(ApprovalRequest::class, Employee::class, 'manager_id');
    }

    public function managedEmployeesByDepartmentLeaves(): HasManyThrough
    {
        return $this->hasManyThrough(Leave::class, Department::class, 'manager_id');
    }

    public function managedEmployeesByDepartmentApprovalRequests(): HasManyThrough
    {
        return $this->hasManyThrough(ApprovalRequest::class, Department::class, 'manager_id');
    }

    public function managedEmployeesByDepartment(): HasManyThrough
    {
        return $this->hasManyThrough(Employee::class, Department::class, 'manager_id');
    }

    protected function email(): Attribute
    {
        return Attribute::set(fn($value) => strtolower($value));
    }

    public function scopeSearch(Builder $q, array|string|null $search = null): Builder
    {
        if (blank($search)) {
            return $q;
        }

        $searchableColumns = ['first_name', 'last_name', 'email', 'number'];

        return $q->where(
            fn($q) => collect($search)
                ->filter()
                ->map('strtolower')
                ->each(fn($text) => $q->orWhereAny($searchableColumns, 'LIKE', "%$text%"))
        );
    }

    public function scopeFilterByTags(Builder $query, string|array|null $tags = null): Builder
    {
        return $query->when(
            $tags,
            fn(Builder $query) => $query->whereHas(
                'tags',
                fn(Builder $query) => $query->whereIn('tags.id', Arr::wrap($tags))
            )
        );
    }

    public function scopeFilterByLocations(
        Builder $query,
        string|array|null $locations = null
    ): Builder {
        return $query->when(
            $locations,
            fn(Builder $query) => $query->whereHas(
                'locations',
                fn(Builder $query) => $query->whereIn('locations.id', Arr::wrap($locations))
            )
        );
    }

    public function scopeFilterByShifts(Builder $query, string|array|null $shifts = null): Builder
    {
        return $query->when(
            $shifts,
            fn(Builder $query) => $query->whereHas(
                'shifts',
                fn(Builder $query) => $query->whereIn('shifts.id', Arr::wrap($shifts))
            )
        );
    }

    public function scopeFilterByDepartments(
        Builder $query,
        string|array|null $departments = null
    ): Builder {
        return $query->when(
            $departments,
            fn(Builder $query) => $query->whereHas(
                'department',
                fn(Builder $query) => $query->whereIn('departments.id', Arr::wrap($departments))
            )
        );
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function scopeExcludeTags(Builder $query, array|Collection $excludedTagsIDs): Builder
    {
        return $query->where(
            fn(Builder $query) => $query
                ->whereDoesntHave('tags')
                ->orWhereHas('tags', fn($query) => $query->whereNotIn('tags.id', $excludedTagsIDs))
        );
    }

    public function preferredLocale()
    {
        return $this->preferred_language;
    }

    public function activities(): HasMany
    {
        return $this->hasMany(Activity::class)->withoutGlobalScopes();
    }

    public function approvalRequests(): HasMany
    {
        return $this->hasMany(ApprovalRequest::class)->withoutGlobalScopes();
    }

    public function leaves(): HasMany
    {
        return $this->hasMany(Leave::class);
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class)->withoutGlobalScopes();
    }

    public function activeAttendanceRecord(): HasOne
    {
        return $this->hasOne(Attendance::class)
            ->oldest()
            ->where('active_until', '>', now());
    }

    public function firstOrCreateActiveAttendanceRecord(): ?Attendance
    {
        if ($this->activeAttendanceRecord) {
            return $this->activeAttendanceRecord;
        }

        PrepareEmployeeAttendanceRecord::dispatchSync($this);

        return $this->load('activeAttendanceRecord')->activeAttendanceRecord;
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class)
            ->withoutGlobalScopes()
            ->withDefault([
                'name' => __('No Department'),
            ]);
    }

    public function managedDepartments(): HasMany
    {
        return $this->hasMany(Department::class, 'manager_id');
    }

    public function manager(): HasOneThrough
    {
        return $this->hasOneThrough(
            Employee::class,
            EmployeeManagerView::class,
            'employee_id',
            'id',
            'id',
            'current_manager_id'
        );
    }

    public function managedEmployees(): HasManyThrough
    {
        return $this->hasManyThrough(
            Employee::class,
            EmployeeManagerView::class,
            'current_manager_id',
            'id',
            'id',
            'employee_id'
        );
    }

    public function employeesApprovalRequests(): HasManyThrough
    {
        return $this->hasManyThrough(
            ApprovalRequest::class,
            EmployeeManagerView::class,
            'current_manager_id',
            'employee_id',
            'id',
            'employee_id'
        );
    }

    public function managedEmployeesLeaves(): HasManyThrough
    {
        return $this->hasManyThrough(
            Leave::class,
            EmployeeManagerView::class,
            'current_manager_id',
            'employee_id',
            'id',
            'employee_id'
        );
    }

    public function shift(): HasOneThrough
    {
        return $this->hasOneThrough(
            Shift::class,
            EmployeeShift::class,
            'employee_id',
            'id',
            'id',
            'shift_id'
        )
            ->where(
                fn($query) => $query
                    // Check if start_at and end_at are both null for permanent shift
                    ->where(fn($q) => $q->whereNull('start_at')->whereNull('end_at'))
                    // OR check for a temporary shift that is active now
                    ->orWhere(
                        fn($q) => $q->where('start_at', '<=', now())->where('end_at', '>=', now())
                    )
            )
            // fetch temporary shift if employee permanent and temporary shifts exist
            ->orderByRaw('start_at IS NOT NULL DESC');
    }

    public function shifts(): BelongsToMany
    {
        return $this->belongsToMany(Shift::class)
            ->using(EmployeeShift::class)
            ->withPivot('id', 'employee_id', 'shift_id', 'permanent', 'start_at', 'end_at')
            ->withTimestamps();
    }

    /**
     * list upcoming shifts including today shift
     */
    public function upcomingShifts(): BelongsToMany
    {
        return $this->belongsToMany(Shift::class)
            ->using(EmployeeShift::class)
            ->withPivot('id', 'employee_id', 'shift_id', 'permanent', 'start_at', 'end_at')
            ->where(
                fn($query) => $query
                    // Check if start_at and end_at are both null for permanent shift
                    ->where(fn($q) => $q->whereNull('start_at')->whereNull('end_at'))
                    // OR check for a temporary shift that is active now and in the future
                    ->orWhere(
                        fn($q) => $q
                            ->where('start_at', '>=', today())
                            ->orWhere('end_at', '>=', today())
                    )
            )
            ->withTimestamps();
    }

    public function proofs(): HasMany
    {
        return $this->hasMany(Proof::class)->withoutGlobalScopes();
    }

    public function getRemoteWorkPolicyAttribute()
    {
        $value = $this->remote_work;

        if ($value == 'inherited') {
            $value = $this->department?->remote_work;
        }

        if ($value == 'inherited') {
            $value = $this->team?->remote_work;
        }

        return $value;
    }

    public function getStatusAttribute(): string
    {
        return $this->isCheckedIn() ? 'CHECK_IN' : 'CHECK_OUT';
    }

    public function getDepartmentNameAttribute()
    {
        return data_get($this, 'department.name');
    }

    public function isCheckedIn()
    {
        return $this->activeAttendanceRecord?->on_duty;
    }

    public function getLastAttendanceAttribute()
    {
        return $this->attendances()
            ->where('status', Attendance::PRESENT)
            ->orderBy('created_at', 'DESC')
            ->first();
    }

    public function getNameAttribute()
    {
        if (isset($this->attributes['name'])) {
            return $this->attributes['name'];
        }

        return $this->first_name . ' ' . $this->last_name;
    }

    public function routeNotificationForOneSignal(): ?array
    {
        return config('services.onesignal.enabled')
            ? ['include_external_user_ids' => ["{$this->id}"]]
            : null;
    }

    public function checkLocation($lat, $lng, bool $isAutomatic = false)
    {
        $inside_location = false;

        $employeeLocations = $this->locations()
            ->activeLocations()
            ->when($isAutomatic, fn($query) => $query->automatic())
            ->get();

        if ($employeeLocations->isEmpty()) {
            $employeeLocations = $this->team->locations;
        }

        foreach ($employeeLocations as $location) {
            $margin = $isAutomatic ? $location->check_out_radius : 0;

            if ($location->isWithinLocation($lat, $lng, $margin)) {
                $inside_location = $location->id;
                break;
            }
        }

        return $inside_location;
    }

    public function reachedRemoteWorkDaysLimits(): bool
    {
        $remoteWorkBalance = EmployeeBalanceCalculator::remoteWorkBalance(
            $this,
            CarbonPeriod::create(now()->startOfMonth(), now()->endOfMonth())
        );

        // when remote work is disabled, the employee can't check in remotely
        if (!$remoteWorkBalance) {
            return true;
        }

        // when current balance is null, means the employee is always allowed to check in remotely
        if (is_null($remoteWorkBalance->currentBalance)) {
            return false;
        }

        // otherwise, check employee balance
        return $remoteWorkBalance->currentBalance <= 0;
    }

    public function hasPendingRemoteWorkApprovalRequest(string $attendanceType): bool
    {
        return $this->approvalRequests()
            ->remoteWork()
            ->pending()
            ->where('attendance_type', $attendanceType)
            ->whereBetween('created_at', [now()->startOfDay(), now()->endOfDay()])
            ->exists();
    }

    public function locations(): MorphToMany
    {
        return $this->morphToMany(Location::class, 'locationable')
            ->withPivot('id', 'permanent', 'start_date', 'end_date')
            ->withTimestamps();
    }

    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }

    public function workScheduleRecords(): HasMany
    {
        return $this->hasMany(WorkScheduleRecord::class, 'employee_id');
    }

    public function activeLocations(): MorphToMany
    {
        return $this->locations()->where(function ($query) {
            $query->where('locationables.permanent', true)->orWhere(function ($q) {
                $q->whereDate('locationables.start_date', '<=', now()->format('Y-m-d'))->whereDate(
                    'locationables.end_date',
                    '>=',
                    now()->format('Y-m-d')
                );
            });
        });
    }

    /**
     * list upcoming locations including today location
     */
    public function upcomingLocations(): MorphToMany
    {
        return $this->locations()->where(function ($query) {
            $query->where('locationables.permanent', true)->orWhere(function ($q) {
                $q->whereDate(
                    'locationables.start_date',
                    '>=',
                    today()->format('Y-m-d')
                )->orWhereDate('locationables.end_date', '>=', today()->format('Y-m-d'));
            });
        });
    }
}
