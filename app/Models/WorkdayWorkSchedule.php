<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\Pivot;

class WorkdayWorkSchedule extends Pivot
{
    use HasFactory;

    protected $fillable = [
        'workday_id',
        'work_schedule_id',
        'selected_specific_days',
        'work_days_number',
        'off_days_number',
        'repetitions_number',
    ];

    protected $casts = [
        'selected_specific_days' => 'array',
    ];
}
