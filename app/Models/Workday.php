<?php

namespace App\Models;

use App\Traits\BelongsToTenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Workday extends Model implements AuditableContract
{
    use BelongsToTenant;
    use HasFactory;
    use Auditable;

    protected $fillable = [
        'name',
        'start_time',
        'end_time',
        'color',
        'flexible_time_before',
        'flexible_time_after',
        'prevent_checkout_after',
    ];

    protected $casts = [
        'start_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
        'flexible_time_before' => 'datetime:H:i:s',
        'flexible_time_after' => 'datetime:H:i:s',
        'prevent_checkout_after' => 'datetime:H:i:s',
    ];

    public function workSchedules(): BelongsToMany
    {
        return $this->belongsToMany(
            WorkSchedule::class,
            'workday_work_schedule',
            'workday_id',
            'work_schedule_id'
        )
            ->using(WorkdayWorkSchedule::class)
            ->withPivot(
                'id',
                'work_schedule_id',
                'workday_id',
                'work_days_number',
                'off_days_number',
                'repetitions_number'
            )
            ->withTimestamps();
    }

    protected function startTimeIncludingFlexibleTime(): Attribute
    {
        return Attribute::get(
            fn() => $this->start_time->subSeconds(
                $this->flexible_time_before->secondsSinceMidnight()
            )
        );
    }

    protected function endTimeIncludingFlexibleTime(): Attribute
    {
        return Attribute::get(
            fn() => $this->end_time->addSeconds($this->flexible_time_after->secondsSinceMidnight())
        );
    }

    public function scopeSearch(Builder $query, string|array $search = null): Builder
    {
        if (blank($search)) {
            return $query;
        }

        $searchTerms = collect($search);

        foreach ($searchTerms as $searchTerm) {
            $query->orWhere('name', 'like', "%$searchTerm%");
        }

        return $query;
    }
}
