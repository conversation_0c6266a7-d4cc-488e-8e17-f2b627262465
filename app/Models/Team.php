<?php

namespace App\Models;

use App\DTOs\EarlyLateConfig;
use App\DTOs\EmployeeStatementConfig;
use App\DTOs\RandomProofNotificationConfig;
use App\Enums\ApprovalType;
use App\Enums\CheckoutReminderConfig;
use App\Enums\SoftwareFeatureCode;
use App\Traits\HasNawartUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Team extends Authenticatable implements AuditableContract
{
    use Auditable;
    use HasApiTokens;
    use HasFactory;
    use HasNawartUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'active',
        'remote_work',
        'free_checkout',
        'force_checkout',
        'primary_color',
        'email',
        'employees_weekly_summary',
        'approval_requests_limit',
        'leave_request',
        'approval_request',
        'permission_request',
        'permission_request_monthly_limit_hours',
        'permission_request_daily_limit_hours',
        'remote_work_days_yearly_limit',
        'remote_work_days_monthly_limit',
        'remote_work_days_weekly_limit',
        'vacation_weekend',
        'nawart_uuid',
        'checkout_reminder_config',
        'map_report_thresholds',
        'colored_logo_url',
        'white_logo_url',
        'early_late_config',
        'random_proof_notification_config',
        'approval_type',
        'employee_statement_config',
        'new_work_schedule_feature_enabled',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'active' => 'boolean',
        'checkout_reminder_config' => CheckoutReminderConfig::class,
        'map_report_thresholds' => 'collection',
        'early_late_config' => EarlyLateConfig::class,
        'random_proof_notification_config' => RandomProofNotificationConfig::class,
        'permission_request_monthly_limit_hours' => 'integer',
        'permission_request_daily_limit_hours' => 'integer',
        'employees_weekly_summary' => 'boolean',
        'vacation_weekend' => 'boolean',
        'leave_request' => 'boolean',
        'approval_request' => 'boolean',
        'permission_request' => 'boolean',
        'approval_type' => ApprovalType::class,
        'employee_statement_config' => EmployeeStatementConfig::class,
        'new_work_schedule_feature_enabled' => 'boolean',
    ];

    public function scopeActive(Builder $query): Builder
    {
        return $query
            ->where('active', true)
            // database has only subscription item for attendance, so when team has a subscription item,
            // it means the team has an active subscription, at least until we take subscription expiration into account
            ->has('subscription.subscriptionItem');
    }

    protected function activeAndHasSubscription(): Attribute
    {
        return Attribute::get(
            // we may need to find a better way to check if the team has an active subscription
            get: fn() => $this->active && $this->subscription?->subscriptionItem?->exists()
        );
    }

    public function scopeEarlyLateEnabled(Builder $query): Builder
    {
        return $query->where('early_late_config->enabled', true);
    }

    public function hasFeature(SoftwareFeatureCode $softwareFeatureCode): bool
    {
        return $this->subscription->subscriptionItem->softwarePackage
            ->softwareFeatures()
            ->where('code', $softwareFeatureCode)
            ->exists();
    }

    public function firstOrCreateDefaultShift(): Shift
    {
        return Shift::firstOrCreate(
            [
                'team_id' => $this->id,
                'is_default' => true,
            ],
            [
                'name' => 'Default',
                'working_hours' => Shift::defaultWorkingHours(),
            ]
        );
    }

    public function resolveAdmin()
    {
        $admin = $this->employees()->active()->whereJsonContains('roles', 'start-admin')->first();

        return $admin ?? $this->employees()->active()->oldest()->first();
    }

    public function employees()
    {
        return $this->hasMany(Employee::class);
    }

    public function webhooks()
    {
        return $this->hasMany(Webhook::class);
    }

    public function webhookJobs(): HasManyThrough
    {
        return $this->hasManyThrough(WebhookJob::class, Webhook::class);
    }

    public function reportTasks()
    {
        return $this->hasMany(ReportTask::class);
    }

    public function devices(): HasMany
    {
        return $this->hasMany(Device::class);
    }

    public function departments()
    {
        return $this->hasMany(Department::class);
    }

    public function locations()
    {
        return $this->hasMany(Location::class);
    }

    public function defaultLocation(): HasOne
    {
        return $this->hasOne(Location::class)->where('is_default', true);
    }

    public function shifts()
    {
        return $this->hasMany(Shift::class);
    }

    public function defaultShift()
    {
        return $this->hasOne(Shift::class)->where('is_default', true);
    }

    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

    public function attendances()
    {
        return $this->hasMany(Attendance::class);
    }

    public function subscription(): HasOne
    {
        return $this->hasOne(Subscription::class);
    }

    public function subscriptionItems(): HasMany
    {
        return $this->hasMany(SubscriptionItem::class);
    }

    public function approvalRequests(): HasMany
    {
        return $this->hasMany(ApprovalRequest::class);
    }

    public function leaves(): HasMany
    {
        return $this->hasMany(Leave::class);
    }

    public function proofs(): HasMany
    {
        return $this->hasMany(Proof::class);
    }
}
