<?php

namespace App\Models;

use App\Calculations\EarlyLateCalculator;
use App\Enums\AttendanceStatus;
use App\Traits\BelongsToTenant;
use Arr;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\NoReturn;
use OwenIt\Auditing\Auditable;
use OwenIt\Auditing\Contracts\Auditable as AuditableContract;

class Attendance extends Model implements AuditableContract
{
    use Auditable;
    use BelongsToTenant;
    use HasFactory;

    const PRESENT = 'PRESENT';

    const ABSENT = 'ABSENT';

    const YET = 'not_yet';

    const WEEKEND = 'WEEKEND';

    const LEAVE = 'LEAVE';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'team_id' => 'integer',
        'employee_id' => 'integer',
        'check_in' => 'datetime',
        'check_out' => 'datetime',
        'net_hours' => 'datetime:H:i:s',
        'shift_from' => 'datetime',
        'shift_to' => 'datetime',
        'on_duty' => 'boolean',
        'random_notifications' => 'boolean',
        'is_adjusted' => 'boolean',
        'date' => 'date:Y-m-d',
        'active_until' => 'datetime',
        'employee_statement_enabled' => 'boolean',
    ];

    public function scopeFilterByShifts(Builder $query, string|array|null $shifts = null): Builder
    {
        return $query->when(
            $shifts,
            fn(Builder $query) => $query->whereHas(
                'shift',
                fn(Builder $query) => $query->whereIn(
                    $this->qualifyColumn('shift_id'),
                    Arr::wrap($shifts)
                )
            )
        );
    }

    public function scopeFilterByLocations(
        Builder $query,
        string|array|null $locations = null
    ): Builder {
        return $query->when(
            $locations,
            fn(Builder $query) => $query->where(
                fn(Builder $query) => $query
                    ->whereIn('check_in_location_id', Arr::wrap($locations))
                    ->orWhereIn('check_out_location_id', Arr::wrap($locations))
            )
        );
    }

    protected function dayOfWeek(): Attribute
    {
        return Attribute::get(fn() => $this->date->dayOfWeek());
    }

    public function scopeOfEmployeesOfManager(Builder $query, Employee $manager): Builder
    {
        $employeesQuery = $manager->managedEmployees()->active();

        return $query->whereIn(
            $this->qualifyColumn('employee_id'),
            fn($query) => $query->select('id')->fromSub($employeesQuery, 'employees')
        );
    }

    public function resetCheckinState(): void
    {
        $this->update([
            'check_in' => null,
            'in_type' => null,
            'status' => Attendance::YET,
            'on_duty' => false,
            'check_in_location_id' => null,
        ]);
    }

    /**
     * Useful for debugging
     */
    #[NoReturn]
    public static function ddFormatted(self|Collection $attendances, string $key = null): void
    {
        dd(
            Collection::make($attendances)->map(function ($attendance) use ($key) {
                $attributes = [
                    'id' => $attendance->id,
                    'employee_name' => $attendance->employee->first_name,
                    'date' => $attendance->date->format('Y-m-d'),
                    'status' => $attendance->status,
                    'check_in' => $attendance->check_in?->format('Y-m-d H:i:s'),
                    'check_out' => $attendance->check_out?->format('Y-m-d H:i:s'),
                    'in_type' => $attendance->in_type,
                    'out_type' => $attendance->out_type,
                    'shift_from' => $attendance->shift_from?->format('Y-m-d H:i:s'),
                    'shift_to' => $attendance->shift_to?->format('Y-m-d H:i:s'),
                    'net_hours' => $attendance->net_hours?->format('H:i:s'),
                    'flexible_hours' => $attendance->flexible_hours,
                    'active_until' => $attendance->active_until?->format('Y-m-d H:i:s'),
                    'on_duty' => $attendance->on_duty,
                    'is_weekend' => $attendance->is_weekend,
                ];

                return $key ? $attributes[$key] : $attributes;
            })
        );
    }

    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function shift(): BelongsTo
    {
        return $this->belongsTo(Shift::class);
    }

    public function leave(): HasOne
    {
        return $this->hasOne(Leave::class, 'employee_id', 'employee_id')
            ->whereDate('from_date', '<=', $this->date)
            ->whereDate('to_date', '>=', $this->date)
            ->latest();
    }

    public function employeeStatement(): HasOne
    {
        return $this->hasOne(EmployeeStatement::class);
    }

    public function checkInLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'check_in_location_id');
    }

    public function checkoutLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, 'check_out_location_id');
    }

    public function checkInDevice(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'check_in_device_id');
    }

    public function checkoutDevice(): BelongsTo
    {
        return $this->belongsTo(Device::class, 'check_out_device_id');
    }

    public function workScheduleRecord(): BelongsTo
    {
        return $this->belongsTo(WorkScheduleRecord::class);
    }

    public function isCheckinRemote(): Attribute
    {
        return Attribute::get(
            fn() => $this->status === 'PRESENT' &&
                in_array($this->in_type, ['REMOTE_CHECK_IN', 'REMOTE_CHECK_IN_APPROVED'])
        );
    }

    public function isCheckoutRemote(): Attribute
    {
        return Attribute::get(
            fn() => $this->status === 'PRESENT' &&
                in_array($this->out_type, ['REMOTE_CHECK_OUT', 'REMOTE_CHECK_OUT_APPROVED'])
        );
    }

    public function consideredAsAbsent(): Attribute
    {
        return Attribute::get(
            fn() => $this->status === AttendanceStatus::ABSENT->value ||
                ($this->status === AttendanceStatus::YET->value && $this->shift_to->isPast())
        );
    }

    protected function isUpcomingShift(): Attribute
    {
        return Attribute::get(fn() => $this->shift_from->isFuture());
    }

    protected function isFulfilled(): Attribute
    {
        return Attribute::get(function () {
            // an attendance is fulfilled if employee is present
            // and worked more than or equal to the committed hours
            if ($this->status === AttendanceStatus::PRESENT->value) {
                return $this->noneCompleteWorkHours()->isEmpty();
            }

            // or when employee is in excused status (e.g. leave, holiday, weekend)
            return in_array(
                AttendanceStatus::from($this->status),
                AttendanceStatus::excusedStatuses()
            );
        });
    }

    public function flexibleShiftEnd(): Attribute
    {
        return Attribute::get(fn() => $this->shift_to->addMinutes($this->flexible_hours));
    }

    public function getCommittedHoursAttribute()
    {
        return $this->shift_from->diffInSeconds($this->shift_to);
    }

    public function getActualHoursInSecAttribute()
    {
        $interval = CarbonInterval::createFromFormat('H:i:s', $this->net_hours->format('H:i:s'));

        return $interval->total('seconds');
    }

    /**
     * @deprecated
     */
    public function getActualHoursPctAttribute()
    {
        $actual = $this->actual_hours_in_sec;
        $committed = $this->committed_hours;

        if ($committed == 0) {
            return 0;
        }

        return ($actual / $committed) * 100;
    }

    public function getStatusNameAttribute()
    {
        $isRemote = $this->is_checkin_remote || $this->is_checkout_remote;

        return match ($this->status) {
            AttendanceStatus::PRESENT->value => $this->is_adjusted
                ? 'RR'
                : ($isRemote
                    ? 'RW'
                    : 'P'),
            AttendanceStatus::ABSENT->value => 'A',
            AttendanceStatus::LEAVE->value => 'L',
            AttendanceStatus::WEEKEND->value => 'W',
            AttendanceStatus::HOLIDAY->value => 'H',
            default => '-',
        };
    }

    public function getEarlyInAttribute()
    {
        return '+ ' . (new EarlyLateCalculator())->calculateEarlyIn($this)->format('%H:%I');
    }

    public function getLateInAttribute()
    {
        return '- ' . (new EarlyLateCalculator())->calculateLateIn($this)->format('%H:%I');
    }

    public function getEarlyOutAttribute()
    {
        return '- ' . (new EarlyLateCalculator())->calculateEarlyOut($this)->format('%H:%I');
    }

    public function getLateOutAttribute()
    {
        return '+ ' . (new EarlyLateCalculator())->calculateLateOut($this)->format('%H:%I');
    }

    public function shiftDuration(): Attribute
    {
        return Attribute::get(
            fn() => $this->shift_from->diffAsCarbonInterval($this->shift_to)
        )->withoutObjectCaching();
    }

    public function noneCompleteWorkHours(): CarbonInterval
    {
        return $this->shift_duration->greaterThan($this->net_hours_as_carbon_interval)
            ? $this->shift_duration->subtract($this->net_hours_as_carbon_interval)
            : CarbonInterval::seconds(0);
    }

    public function actualDurationWithinShift(): CarbonInterval
    {
        if (
            !$this->check_out ||
            $this->check_out->lessThan($this->shift_from) ||
            $this->shift_to->lessThan($this->check_in)
        ) {
            return CarbonInterval::seconds(0);
        }

        /** @var Carbon $start */
        $start = max([$this->shift_from->subMinutes($this->flexible_hours), $this->check_in]);
        /** @var Carbon $end */
        $end = min([$this->shift_to->addMinutes($this->flexible_hours), $this->check_out]);

        return $start->diffAsCarbonInterval($end);
    }

    public function scopeDate(
        Builder $query,
        string|Carbon|CarbonImmutable|CarbonPeriod $date
    ): Builder {
        if ($date instanceof CarbonPeriod) {
            return $query->whereBetween('date', [$date->start, $date->end]);
        }

        return $query->where('date', Carbon::parse($date)->format('Y-m-d'));
    }

    public function netHoursAsCarbonInterval(): Attribute
    {
        return Attribute::get(
            fn() => CarbonInterval::createFromFormat('H:i:s', $this->net_hours->format('H:i:s'))
        )->withoutObjectCaching();
    }

    public function singleSheetBackgroundColor(): ?string
    {
        if (!$this->employee->is_active) {
            return 'D9D9D9';
        }

        return match ($this->singleSheetComputedStatus()) {
            AttendanceStatus::ABSENT->value => 'BFBFBF',
            AttendanceStatus::LEAVE->value => 'C5D9F1',
            AttendanceStatus::HOLIDAY->value => 'C4D79B',
            AttendanceStatus::WEEKEND->value => 'FFFFA8',
            'Forget to check-out' => 'F79646',
            default => null,
        };
    }

    public function singleSheetComputedStatus(): ?string
    {
        if ($this->status !== AttendanceStatus::PRESENT->value) {
            return $this->status;
        }

        if (!$this->out_type) {
            return 'Forget to check-out';
        }

        if ($this->is_checkin_remote || $this->is_checkout_remote) {
            return 'Remote Work';
        }

        return $this->status;
    }

    public function scopeOfLeave(Builder $query, Leave $leave): Builder
    {
        return $query
            ->where('employee_id', $leave->employee_id)
            ->whereBetween('date', [$leave->from_date, $leave->to_date]);
    }

    public function scopePresent(Builder $query): Builder
    {
        return $query->where('status', AttendanceStatus::PRESENT->value);
    }

    public function scopeAbsent(Builder $query): Builder
    {
        return $query->where('status', AttendanceStatus::ABSENT->value);
    }

    public function scopeLeave(Builder $query): Builder
    {
        return $query->where('status', AttendanceStatus::LEAVE->value);
    }

    public function scopeYet(Builder $query): Builder
    {
        return $query->where('status', AttendanceStatus::YET);
    }
}
