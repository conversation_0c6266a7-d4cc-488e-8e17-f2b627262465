<?php

namespace App\Enums;

use App\Models\Team;

enum AttendanceStatus: string
{
    case PRESENT = 'PRESENT';
    case ABSENT = 'ABSENT';
    case YET = 'not_yet';
    case WEEKEND = 'WEEKEND';
    case HOLIDAY = 'HOLIDAY';
    case LEAVE = 'LEAVE';

    public static function statusesShouldBeLeaveByTeam(Team $team): array
    {
        // when team has vacation_weekend, weekend is considered as leave,
        // so we should consider it as leave in attendance status
        return $team->vacation_weekend
            ? [AttendanceStatus::ABSENT, AttendanceStatus::YET, AttendanceStatus::WEEKEND]
            : [AttendanceStatus::ABSENT, AttendanceStatus::YET];
    }

    public static function fromWorkdayType(WorkdayType $workdayType): self
    {
        return match ($workdayType) {
            WorkdayType::Weekend => AttendanceStatus::WEEKEND,
            WorkdayType::Holiday => AttendanceStatus::HOLIDAY,
            WorkdayType::Leave => AttendanceStatus::LEAVE,
            WorkdayType::Weekday => AttendanceStatus::YET,
        };
    }

    public static function excusedStatuses(): array
    {
        // statuses that are considered as excused and should not negatively affect attendance stats
        return [
            AttendanceStatus::WEEKEND,
            AttendanceStatus::HOLIDAY,
            AttendanceStatus::LEAVE,
            AttendanceStatus::YET,
        ];
    }
}
