<?php

namespace App\Exports\MultiSheet;

use App\Enums\AttendanceStatus;
use App\Interfaces\ExcelPart;
use App\Interfaces\ExcelPartCreator;
use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\ReportTask;
use App\Support\ExcelStyles;
use App\Support\IntervalFormat;
use Carbon\CarbonInterval;
use Illuminate\Support\Collection;

class MultiSheetEarlyLatePartCreator implements ExcelPartCreator
{
    public function createPart(ReportTask $reportTask, ExcelPart $part): array
    {
        return $part
            ->fetchPartValues($reportTask)
            ->map(
                fn(Employee $employee) => [
                    'employee' => [
                        'name' => $employee->name,
                        'email' => $employee->email,
                        'number' => $employee->number,
                        'department_name' => $employee->department?->name,
                        'manager_name' => $employee->manager?->name,
                    ],
                    'summary' => $this->summary($employee->attendances),
                    'attendance_rows' => $employee->attendances->map(
                        fn($att) => $this->buildCells($att)
                    ),
                ]
            )
            ->toArray();
    }

    protected function buildCells(Attendance $attendance): array
    {
        $status = match ($attendance->status) {
            Attendance::PRESENT => $attendance->out_type === null ? 'FORGET_CHECKOUT' : 'PRESENT',
            Attendance::WEEKEND => 'WEEKEND',
            AttendanceStatus::HOLIDAY->value => 'OFFICIAL_HOLIDAY',
            Attendance::ABSENT => 'ABSENT',
            Attendance::LEAVE => 'LEAVE',
            default => 'NOT_YET',
        };

        return [
            $attendance->date->format('j/m/Y'),
            $attendance->check_in?->format('g:i a'),
            $attendance->check_out?->format('g:i a'),
            $attendance->net_hours->format('G:i'),
            $attendance->early_in,
            $attendance->late_in,
            $attendance->early_out,
            $attendance->late_out,
            $attendance->shift?->name ?? $attendance->workScheduleRecord?->workday?->name,
            $attendance->checkInDevice?->name ?? config('app.default_device_name'),
            $attendance->have_location
                ? ($attendance->is_checkin_remote
                    ? 'Remotely'
                    : $attendance->checkInLocation?->name)
                : null,
            $attendance->checkoutDevice?->name ?? config('app.default_device_name'),
            $attendance->have_location
                ? ($attendance->is_checkin_remote
                    ? 'Remotely'
                    : $attendance->checkoutLocation?->name)
                : null,
            $status, // to be used in the conditional formatting
            ExcelStyles::ATTENDANCE_STATUS_COLOR[$status] ?? null, // to be used in the conditional formatting
        ];
    }

    protected function summary(Collection $attendances): array
    {
        $sums = $this->getSums($attendances);

        return [
            'officialHolidayCount' => $attendances
                ->where('status', AttendanceStatus::HOLIDAY->value)
                ->count(),
            'totalLateIn' => IntervalFormat::toHoursMinutes($sums['lateIn']->cascade()),
            'totalEarlyOut' => IntervalFormat::toHoursMinutes($sums['earlyOut']->cascade()),
            'totalActualHours' => IntervalFormat::toHoursMinutes(
                $sums['totalActualHours']->cascade()
            ),
            'totalAdditionalHours' => IntervalFormat::toHoursMinutes(
                $sums['totalAdditionalHours']->cascade()
            ),
            'totalAdditionalHoursDayByDay' => IntervalFormat::toHoursMinutes(
                $sums['totalAdditionalHoursDayByDay']->cascade()
            ),
            'noneCompleteWorkHours' => IntervalFormat::toHoursMinutes(
                $sums['noneCompleteWorkHours']->cascade()
            ),
            'absentCount' => $attendances->where('status', Attendance::ABSENT)->count(),
            'forgotToCheckoutCount' => $attendances
                ->where('status', Attendance::PRESENT)
                ->whereNull('out_type')
                ->count(),
            'leaveCount' => $attendances->where('status', Attendance::LEAVE)->count(),
            'remoteWorkCount' => $attendances->where('in_type', Activity::REMOTE_CHECK_IN)->count(),
        ];
    }

    protected function getSums(Collection $attendances): array
    {
        $sums = $attendances->reduce(
            function (array $sums, Attendance $attendance) {
                $sums['lateIn'] = $sums['lateIn']->add(
                    CarbonInterval::createFromFormat('- H:i', $attendance->late_in)
                );

                $sums['earlyOut'] = $sums['earlyOut']->add(
                    CarbonInterval::createFromFormat('- H:i', $attendance->early_out)
                );

                $sums['totalCommittedHours'] = $sums['totalCommittedHours']->addSeconds(
                    $attendance->committed_hours
                );

                $sums['totalActualHours'] = $sums['totalActualHours']->addSeconds(
                    $attendance->actual_hours_in_sec
                );

                $sums['noneCompleteWorkHours'] = $sums['noneCompleteWorkHours']->add(
                    $attendance->noneCompleteWorkHours()
                );

                return $sums;
            },
            [
                'lateIn' => CarbonInterval::hour(0),
                'earlyOut' => CarbonInterval::hour(0),
                'totalCommittedHours' => CarbonInterval::hour(0),
                'totalActualHours' => CarbonInterval::hour(0),
                'noneCompleteWorkHours' => CarbonInterval::hour(0),
            ]
        );

        // calculate additional hours while subtracting if employee worked less than committed hours in a period
        // for example, in a week, if the employee has 8 hours each day, but he worked 7 hours on Monday and 9 hours on Tuesday,
        // the additional hours in Tuesday will subtract 1 hour from Monday so the total additional hours will be 0 hours
        $sums['totalAdditionalHours'] = $sums['totalActualHours']->greaterThan(
            $sums['totalCommittedHours']
        )
            ? $sums['totalActualHours']->sub($sums['totalCommittedHours'])
            : CarbonInterval::seconds(0);

        // calculate additional hours day by day without subtracting if employee worked less than committed hours in another day
        // for example, in a week, if the employee has 8 hours each day, but he worked 7 hours on Monday and 9 hours on Tuesday,
        // then the additional hours day by day will be 2 hours (we don't subtract 1 hour from Monday)
        $sums['totalAdditionalHoursDayByDay'] = $attendances->reduce(function (
            CarbonInterval $sum,
            Attendance $attendance
        ) {
            $actualHours = CarbonInterval::seconds($attendance->actual_hours_in_sec);
            $committedHours = CarbonInterval::seconds($attendance->committed_hours);
            $additionalHours = $actualHours->greaterThan($committedHours)
                ? $actualHours->sub($committedHours)
                : CarbonInterval::seconds(0);

            return $sum->add($additionalHours);
        }, CarbonInterval::seconds(0));

        return $sums;
    }
}
