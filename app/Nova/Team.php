<?php

namespace App\Nova;

use App\Models\Team as TeamModel;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Text;

class Team extends ReadOnlyResource
{
    public static $model = TeamModel::class;

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
        'remote_work',
        'free_checkout',
        'primary_color',
        'nawart_uuid',
    ];

    public function fields(Request $request): array
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->sortable()->rules('required'),

            Text::make('Remote Work')->sortable()->rules('required'),

            Text::make('Free Checkout')->sortable()->rules('required'),

            Text::make('Primary Color')->sortable()->rules('required'),

            Number::make('Approval Requests Limit')->sortable()->rules('required', 'integer'),

            Text::make('Random Notification')->sortable()->rules('required'),

            Number::make('Remote Work Days Monthly Limit')
                ->sortable()
                ->rules('required', 'integer'),

            Text::make('Nawart Uuid')->sortable()->rules('required'),

            Boolean::make('New Work Schedule Feature Enabled')->sortable()->rules('required'),

            HasMany::make('Activities', 'activities', Activity::class),

            HasMany::make('Attendances', 'attendances', Attendance::class),

            HasOne::make('DefaultLocation', 'defaultLocation', Location::class),

            HasOne::make('DefaultShift', 'defaultShift', Shift::class),

            HasMany::make('Departments', 'departments', Department::class),

            HasMany::make('Employees', 'employees', Employee::class),

            HasMany::make('Locations', 'locations'),

            HasMany::make('Shifts', 'shifts'),

            HasMany::make('Devices', 'devices'),

            HasMany::make('ReportTasks', 'reportTasks', ReportTask::class),

            HasMany::make('WebhooksJobs', 'webhookJobs', WebhookJobResource::class),

            HasMany::make('Webhooks', 'webhooks', WebhookResource::class),
        ];
    }

    public function cards(Request $request): array
    {
        return [];
    }

    public function filters(Request $request): array
    {
        return [];
    }

    public function lenses(Request $request): array
    {
        return [];
    }

    public function actions(Request $request): array
    {
        return [];
    }
}
