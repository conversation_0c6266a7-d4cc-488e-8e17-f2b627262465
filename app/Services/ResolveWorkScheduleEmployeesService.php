<?php

namespace App\Services;

use App\Enums\WorkScheduleAssignmentType;
use App\Models\Employee;
use App\Models\WorkScheduleAssignment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection as SupportCollection;

class ResolveWorkScheduleEmployeesService
{
    public static function handle(SupportCollection $assignments): Builder
    {
        $employeeIds = collect();

        foreach ($assignments as $assignment) {
            $employeeIds = $employeeIds->merge(self::resolveAssignmentType($assignment));
        }

        return Employee::whereIn('id', $employeeIds->unique())->active();
    }

    private static function resolveAssignmentType(
        WorkScheduleAssignment $assignment
    ): SupportCollection {
        $assignmentIds = $assignment->value->toArray();

        return match ($assignment->type) {
            WorkScheduleAssignmentType::Employee => collect($assignmentIds),
            WorkScheduleAssignmentType::Department => self::resolveDepartmentEmployees(
                $assignmentIds
            ),
            WorkScheduleAssignmentType::DirectManager => self::resolveManagerEmployees(
                $assignmentIds
            ),
            WorkScheduleAssignmentType::Tag => self::resolveTaggedEmployees($assignmentIds),
            WorkScheduleAssignmentType::Location => self::resolveLocationEmployees($assignmentIds),
        };
    }

    private static function resolveDepartmentEmployees(array $departmentIds): SupportCollection
    {
        return Employee::whereIn('department_id', $departmentIds)->pluck('id');
    }

    private static function resolveManagerEmployees(array $managerIds): SupportCollection
    {
        return Employee::whereIn('manager_id', $managerIds)->pluck('id');
    }

    private static function resolveTaggedEmployees(array $tagIds): SupportCollection
    {
        return Employee::whereHas('tags', fn($query) => $query->whereIn('tags.id', $tagIds))->pluck(
            'id'
        );
    }

    private static function resolveLocationEmployees(array $locationIds): SupportCollection
    {
        return Employee::whereHas(
            'locations',
            fn($query) => $query->whereIn('locations.id', $locationIds)
        )->pluck('id');
    }
}
