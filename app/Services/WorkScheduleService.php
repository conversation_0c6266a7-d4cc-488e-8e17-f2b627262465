<?php

namespace App\Services;

use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\WorkSchedule;
use DB;

class WorkScheduleService
{
    public static function process(array $data, ?WorkSchedule $workSchedule = null): WorkSchedule
    {
        return DB::transaction(function () use ($workSchedule, $data) {
            // Extract workdays and assignments data
            $workdaysData = $data['workdays'];
            $assignmentsData = $data['assignments'] ?? null;

            unset($data['workdays'], $data['assignments']);

            if ($workSchedule) {
                $workSchedule->update($data);
            } else {
                $workSchedule = WorkSchedule::create($data);
            }

            // Prepare workdays pivot data
            $workDaysPivotToSync = collect($workdaysData)->mapWithKeys(
                fn($workdayData) => [
                    $workdayData['workday_id'] => [
                        'selected_specific_days' => $workdayData['selected_specific_days'] ?? null,
                        'work_days_number' => $workdayData['work_days_number'] ?? null,
                        'off_days_number' => $workdayData['off_days_number'] ?? null,
                        'repetitions_number' => $workdayData['repetitions_number'] ?? null,
                    ],
                ]
            );

            $workSchedule->workdays()->sync($workDaysPivotToSync);

            WorkScheduleAssignmentsService::process($workSchedule, $assignmentsData);

            if ($assignmentsData) {
                GenerateWorkScheduleRecordsJob::dispatch($workSchedule);
            }

            return $workSchedule;
        });
    }
}
