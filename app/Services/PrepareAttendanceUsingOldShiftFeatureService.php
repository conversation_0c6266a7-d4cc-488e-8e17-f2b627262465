<?php

namespace App\Services;

use App\Enums\AttendanceStatus;
use App\Models\Attendance;
use App\Models\Employee;
use App\Models\Holiday;
use Carbon\CarbonImmutable;

class PrepareAttendanceUsingOldShiftFeatureService
{
    public function __construct(private Employee $employee, private CarbonImmutable $date)
    {
    }

    public function handle(): ?Attendance
    {
        $shift = $this->employee->shift;

        if (!$shift) {
            return null;
        }

        $shiftFrom = $this->date->setTime(0, 0);
        $shiftTo = $this->date->setTime(0, 0);
        $isWeekend = true;
        $status = Attendance::WEEKEND;
        $isNextDayCheckout = false;
        $isHoliday = Holiday::query()
            ->where('team_id', $this->employee->team_id)
            ->whereDate('start_date', '<=', $this->date)
            ->whereDate('end_date', '>=', $this->date)
            ->exists();

        $vacationWeekendPolicy = $this->employee->team->vacation_weekend;

        $todayShiftConfig = $shift->getDayShift($this->date);

        if ($todayShiftConfig) {
            $shiftFrom = $this->date->setTimeFromTimeString($todayShiftConfig['from']);
            $shiftTo = $this->date->setTimeFromTimeString($todayShiftConfig['to']);
            $isNextDayCheckout = !empty($todayShiftConfig['next_day_checkout']);

            if ($isNextDayCheckout) {
                $shiftTo = $shiftTo->copy()->addDay();
            }

            $isWeekend = false;
            $status = $this->date->isToday() ? Attendance::YET : Attendance::ABSENT;
        }

        $forceCheckout = $shift->force_checkout->setDate(
            $this->date->year,
            $this->date->month,
            $this->date->day
        );

        if ($shiftFrom > $forceCheckout) {
            $activeUntil = $forceCheckout->addDay();
        } else {
            $activeUntil = $forceCheckout;
        }

        if ($isWeekend) {
            $activeUntil = $this->date->setTime(23, 59);
        }

        // when user for some reason, put force_checkout "before" shift_to,
        // for example, shift_to is 18:00 and force_checkout is 16:00,
        if ($shiftTo > $activeUntil) {
            $activeUntil = $shiftTo;
        }

        if ($isNextDayCheckout) {
            $activeUntil = $todayShiftConfig['prevent_checkout_after']
                ? $this->date
                    ->setTimeFromTimeString($todayShiftConfig['prevent_checkout_after'])
                    ->addDay()
                : $shiftTo;
        }

        $onLeave = $this->employee
            ->leaves()
            ->date($this->date)
            ->approved()
            ->count();

        $data = $this->getStatus(
            status: $status,
            onLeave: $onLeave,
            isWeekend: $isWeekend,
            isHoliday: $isHoliday,
            vacationWeekendPolicy: $vacationWeekendPolicy
        );

        return Attendance::firstOrCreate(
            [
                'employee_id' => $this->employee->id,
                'date' => $this->date->format('Y-m-d'),
            ],
            [
                'team_id' => $this->employee->team_id,
                'check_in' => null,
                'check_out' => null,
                'shift_id' => $shift->id,
                'shift_from' => $shiftFrom,
                'shift_to' => $shiftTo,
                'net_hours' => $this->date->setTime(0, 0),
                'status' => $data['status'],
                'is_weekend' => $data['is_weekend'],
                'is_holiday' => $data['is_holiday'],
                'active_until' => $activeUntil,
                'flexible_hours' => $shift->working_hours['flexible_hours'],
                'timezone' => $shift->timezone,
                'employee_statement_enabled' =>
                    $this->employee->team->employee_statement_config->enabled,
            ]
        );
    }

    public function getStatus(
        string $status,
        string $onLeave,
        bool $isWeekend,
        bool $isHoliday,
        bool $vacationWeekendPolicy
    ): array {
        if ($onLeave) {
            return [
                'status' =>
                    $isWeekend && !$vacationWeekendPolicy
                        ? AttendanceStatus::WEEKEND
                        : AttendanceStatus::LEAVE,
                'is_weekend' => $isWeekend && !$vacationWeekendPolicy,
                'is_holiday' => false,
            ];
        }

        if ($isHoliday) {
            return [
                'status' => AttendanceStatus::HOLIDAY,
                'is_weekend' => false,
                'is_holiday' => true,
            ];
        }

        return [
            'status' => $isWeekend ? AttendanceStatus::WEEKEND : $status,
            'is_weekend' => $isWeekend,
            'is_holiday' => false,
        ];
    }
}
