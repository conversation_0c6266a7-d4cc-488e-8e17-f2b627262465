/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `action_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `action_events` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `actionable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `actionable_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `target_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `target_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `fields` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'running',
  `exception` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `original` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `changes` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `action_events_actionable_type_actionable_id_index` (`actionable_type`,`actionable_id`),
  KEY `action_events_target_type_target_id_index` (`target_type`,`target_id`),
  KEY `action_events_batch_id_model_type_model_id_index` (`batch_id`,`model_type`,`model_id`),
  KEY `action_events_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `activities` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `employee_id` bigint unsigned NOT NULL,
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `lat` double NOT NULL,
  `lng` double NOT NULL,
  `payload` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `location_id` bigint unsigned DEFAULT NULL,
  `device_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `activities_team_id_foreign` (`team_id`),
  KEY `activities_employee_id_foreign` (`employee_id`),
  KEY `activities_location_id_foreign` (`location_id`),
  CONSTRAINT `activities_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `activities_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`) ON DELETE SET NULL,
  CONSTRAINT `activities_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admins` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admins_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `approval_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `approval_requests` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `employee_id` bigint unsigned NOT NULL,
  `department_id` bigint unsigned DEFAULT NULL,
  `from_datetime` datetime NOT NULL,
  `to_datetime` datetime NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `attendance_type` enum('CHECK_IN','CHECK_OUT','CHECK_IN_OUT') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `rejection_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `approval_requests_team_id_foreign` (`team_id`),
  KEY `approval_requests_employee_id_foreign` (`employee_id`),
  KEY `approval_requests_department_id_foreign` (`department_id`),
  KEY `approval_requests_status_index` (`status`),
  KEY `approval_requests_type_index` (`type`),
  KEY `approval_requests_from_datetime_index` (`from_datetime`),
  KEY `approval_requests_to_datetime_index` (`to_datetime`),
  CONSTRAINT `approval_requests_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `approval_requests_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `approval_requests_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `attendances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendances` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `employee_id` bigint unsigned NOT NULL,
  `shift_id` bigint unsigned NOT NULL,
  `in_mood` tinyint unsigned DEFAULT NULL,
  `out_mood` tinyint unsigned DEFAULT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'not_yet',
  `shift_from` datetime NOT NULL,
  `shift_to` datetime NOT NULL,
  `check_in` datetime DEFAULT NULL,
  `check_out` datetime DEFAULT NULL,
  `net_hours` time NOT NULL,
  `in_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `out_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_weekend` tinyint(1) NOT NULL DEFAULT '0',
  `is_holiday` tinyint(1) NOT NULL DEFAULT '0',
  `force_checkout_time` timestamp NULL DEFAULT NULL,
  `on_duty` tinyint(1) NOT NULL DEFAULT '0',
  `active_until` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `random_notifications` tinyint(1) NOT NULL DEFAULT '0',
  `is_adjusted` tinyint(1) NOT NULL DEFAULT '0',
  `flexible_hours` tinyint unsigned NOT NULL DEFAULT '0',
  `check_in_location_id` bigint unsigned DEFAULT NULL,
  `check_out_location_id` bigint unsigned DEFAULT NULL,
  `have_location` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'Indicate whether attendance''s record existed before or after adding check-in and check-out location.\n                As we will show empty string for check-in/out location for old attendance records only.',
  `date` date NOT NULL,
  `timezone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Asia/Riyadh',
  `employee_statement_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `check_in_device_id` bigint unsigned DEFAULT NULL,
  `check_out_device_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `attendances_team_id_foreign` (`team_id`),
  KEY `attendances_shift_id_foreign` (`shift_id`),
  KEY `attendances_check_in_location_id_foreign` (`check_in_location_id`),
  KEY `attendances_check_out_location_id_foreign` (`check_out_location_id`),
  KEY `attendances_date_index` (`date`),
  KEY `attendances_status_index` (`status`),
  KEY `attendances_employee_id_foreign` (`employee_id`),
  KEY `attendances_check_in_device_id_foreign` (`check_in_device_id`),
  KEY `attendances_check_out_device_id_foreign` (`check_out_device_id`),
  CONSTRAINT `attendances_check_in_device_id_foreign` FOREIGN KEY (`check_in_device_id`) REFERENCES `devices` (`id`),
  CONSTRAINT `attendances_check_in_location_id_foreign` FOREIGN KEY (`check_in_location_id`) REFERENCES `locations` (`id`) ON DELETE SET NULL,
  CONSTRAINT `attendances_check_out_device_id_foreign` FOREIGN KEY (`check_out_device_id`) REFERENCES `devices` (`id`),
  CONSTRAINT `attendances_check_out_location_id_foreign` FOREIGN KEY (`check_out_location_id`) REFERENCES `locations` (`id`) ON DELETE SET NULL,
  CONSTRAINT `attendances_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `attendances_shift_id_foreign` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`id`),
  CONSTRAINT `attendances_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `audits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audits` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `event` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `auditable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `auditable_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_values` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `new_values` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(1023) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `agent` enum('web','mobile','external','nova','console','frontend') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audits_auditable_type_auditable_id_index` (`auditable_type`,`auditable_id`),
  KEY `audits_user_id_user_type_index` (`user_id`,`user_type`),
  KEY `audits_team_id_foreign` (`team_id`),
  CONSTRAINT `audits_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `decisions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `decisions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `status` enum('PENDING','APPROVED','REJECTED') COLLATE utf8mb4_unicode_ci NOT NULL,
  `layer` enum('FIRST','SECOND') COLLATE utf8mb4_unicode_ci NOT NULL,
  `decidable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `decidable_id` bigint unsigned NOT NULL,
  `team_id` bigint unsigned NOT NULL,
  `employee_id` bigint unsigned NOT NULL,
  `decider_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `decisions_decidable_type_decidable_id_index` (`decidable_type`,`decidable_id`),
  KEY `decisions_team_id_foreign` (`team_id`),
  KEY `decisions_employee_id_foreign` (`employee_id`),
  KEY `decisions_decider_id_foreign` (`decider_id`),
  CONSTRAINT `decisions_decider_id_foreign` FOREIGN KEY (`decider_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `decisions_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `decisions_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `delegations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `delegations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `delegated_id` bigint unsigned NOT NULL,
  `delegatee_id` bigint unsigned NOT NULL,
  `type` enum('REMOTE_WORK_REQUEST','LEAVE_REQUEST','REGULARIZATION_REQUEST','PERMISSION_REQUEST','PERIODICAL_EARLY_LATE_REPORT') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `delegations_delegatee_id_type_unique` (`delegatee_id`,`type`),
  UNIQUE KEY `delegations_delegated_id_type_unique` (`delegated_id`,`type`),
  CONSTRAINT `delegations_delegated_id_foreign` FOREIGN KEY (`delegated_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `delegations_delegatee_id_foreign` FOREIGN KEY (`delegatee_id`) REFERENCES `employees` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `departments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `departments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `remote_work` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inherited',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `manager_id` bigint unsigned DEFAULT NULL,
  `nawart_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '(DC2Type:guid)',
  `random_proof_notification_config` json NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `departments_nawart_uuid_unique` (`nawart_uuid`),
  KEY `departments_team_id_foreign` (`team_id`),
  KEY `departments_parent_id_foreign` (`parent_id`),
  KEY `departments_manager_id_foreign` (`manager_id`),
  KEY `departments_nawart_uuid_index` (`nawart_uuid`),
  CONSTRAINT `departments_manager_id_foreign` FOREIGN KEY (`manager_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `departments_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `departments_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `devices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `devices` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `team_id` bigint unsigned NOT NULL,
  `location_selection` enum('existing_location','device_location') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_id` bigint unsigned DEFAULT NULL,
  `username` char(26) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `secret_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `lat` double DEFAULT NULL,
  `lng` double DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `devices_username_unique` (`username`),
  KEY `devices_team_id_foreign` (`team_id`),
  KEY `devices_location_id_foreign` (`location_id`),
  CONSTRAINT `devices_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `devices_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `employee_managers_view`;
/*!50001 DROP VIEW IF EXISTS `employee_managers_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `employee_managers_view` AS SELECT 
 1 AS `employee_id`,
 1 AS `employee_team_id`,
 1 AS `current_manager_id`*/;
SET character_set_client = @saved_cs_client;
DROP TABLE IF EXISTS `employee_shift`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employee_shift` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `employee_id` bigint unsigned NOT NULL,
  `shift_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `permanent` tinyint(1) NOT NULL DEFAULT '1',
  `start_at` datetime DEFAULT NULL,
  `end_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `employee_statements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employee_statements` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `employee_id` bigint unsigned NOT NULL,
  `attendance_id` bigint unsigned NOT NULL,
  `type` enum('late_checkin','early_checkout','late_checkin_and_early_checkout','absent') COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `requestable_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `requestable_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_statements_attendance_id_unique` (`attendance_id`),
  KEY `employee_statements_team_id_foreign` (`team_id`),
  KEY `employee_statements_employee_id_foreign` (`employee_id`),
  KEY `employee_statements_requestable_type_requestable_id_index` (`requestable_type`,`requestable_id`),
  CONSTRAINT `employee_statements_attendance_id_foreign` FOREIGN KEY (`attendance_id`) REFERENCES `attendances` (`id`),
  CONSTRAINT `employee_statements_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `employee_statements_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `employees`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employees` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `team_id` bigint unsigned NOT NULL,
  `department_id` bigint unsigned DEFAULT NULL,
  `shift_id` bigint unsigned DEFAULT NULL,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `remote_work` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inherited',
  `preferred_language` enum('en','ar') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ar',
  `device_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `nawart_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '(DC2Type:guid)',
  `last_activity_at` datetime DEFAULT NULL,
  `first_login_at` datetime DEFAULT NULL,
  `manager_id` bigint unsigned DEFAULT NULL,
  `app_version` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_os` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `roles` json DEFAULT NULL,
  `random_proof_notification_config` json NOT NULL,
  `is_ready` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `employees_nawart_uuid_unique` (`nawart_uuid`),
  UNIQUE KEY `employees_team_id_number_unique` (`team_id`,`number`),
  UNIQUE KEY `employees_team_id_email_unique` (`team_id`,`email`),
  KEY `employees_department_id_foreign` (`department_id`),
  KEY `employees_shift_id_foreign` (`shift_id`),
  KEY `employees_nawart_uuid_index` (`nawart_uuid`),
  KEY `employees_manager_id_foreign` (`manager_id`),
  CONSTRAINT `employees_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `employees_manager_id_foreign` FOREIGN KEY (`manager_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `employees_shift_id_foreign` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`id`),
  CONSTRAINT `employees_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `holidays`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `holidays` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `team_id` bigint unsigned NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `holidays_team_id_foreign` (`team_id`),
  CONSTRAINT `holidays_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `import_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `import_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `import_job_id` bigint unsigned NOT NULL,
  `employee_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `employee_position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `employee_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `employee_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department_manager` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department_parent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkin_location_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkin_location_lat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkin_location_lng` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkin_location_radius` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkout_location_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkout_location_lat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkout_location_lng` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkout_location_radius` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `checkin_datetime` datetime DEFAULT NULL,
  `checkout_datetime` datetime DEFAULT NULL,
  `activity_lat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `activity_lng` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `import_data_import_job_id_foreign` (`import_job_id`),
  CONSTRAINT `import_data_import_job_id_foreign` FOREIGN KEY (`import_job_id`) REFERENCES `import_jobs` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `import_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `import_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `stage` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'initial',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `import_jobs_team_id_foreign` (`team_id`),
  CONSTRAINT `import_jobs_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `invitations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `invitations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `department_id` bigint unsigned DEFAULT NULL,
  `shift_id` bigint unsigned DEFAULT NULL,
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `email_sent_at` timestamp NULL DEFAULT NULL,
  `number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `temporary_shift_id` bigint unsigned DEFAULT NULL,
  `permanent_shift_id` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invitations_team_id_email_unique` (`team_id`,`email`),
  UNIQUE KEY `invitations_team_id_number_unique` (`team_id`,`number`),
  KEY `invitations_department_id_foreign` (`department_id`),
  KEY `invitations_shift_id_foreign` (`shift_id`),
  KEY `invitations_temporary_shift_id_foreign` (`temporary_shift_id`),
  KEY `invitations_permanent_shift_id_foreign` (`permanent_shift_id`),
  CONSTRAINT `invitations_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `invitations_permanent_shift_id_foreign` FOREIGN KEY (`permanent_shift_id`) REFERENCES `shifts` (`id`),
  CONSTRAINT `invitations_shift_id_foreign` FOREIGN KEY (`shift_id`) REFERENCES `shifts` (`id`),
  CONSTRAINT `invitations_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  CONSTRAINT `invitations_temporary_shift_id_foreign` FOREIGN KEY (`temporary_shift_id`) REFERENCES `shifts` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `leaves`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `leaves` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `employee_id` bigint unsigned NOT NULL,
  `department_id` bigint unsigned DEFAULT NULL,
  `from_date` datetime NOT NULL,
  `to_date` datetime NOT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','APPROVED','REJECTED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `is_sync_with_attendance_checked` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `synced_to_attendance_at` datetime DEFAULT NULL,
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `rejection_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `leaves_team_id_foreign` (`team_id`),
  KEY `leaves_employee_id_foreign` (`employee_id`),
  KEY `leaves_department_id_foreign` (`department_id`),
  KEY `leaves_from_date_index` (`from_date`),
  KEY `leaves_to_date_index` (`to_date`),
  KEY `leaves_status_index` (`status`),
  CONSTRAINT `leaves_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`),
  CONSTRAINT `leaves_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `leaves_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `locationables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `locationables` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `locationable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `locationable_id` bigint unsigned NOT NULL,
  `location_id` bigint unsigned NOT NULL,
  `permanent` tinyint(1) NOT NULL,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `locationables_locationable_type_locationable_id_index` (`locationable_type`,`locationable_id`),
  KEY `locationables_location_id_foreign` (`location_id`),
  CONSTRAINT `locationables_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `locations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `locations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `lat` double NOT NULL,
  `lng` double NOT NULL,
  `radius` double NOT NULL,
  `timezone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `check_out_radius` double NOT NULL DEFAULT '0' COMMENT 'extra radius used to extend radius of check-out for this location',
  `automatic` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'whether this location can be used for automatic check-in/out or not',
  PRIMARY KEY (`id`),
  KEY `locations_team_id_foreign` (`team_id`),
  CONSTRAINT `locations_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_field_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_field_attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `attachable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attachable_id` bigint unsigned NOT NULL,
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `disk` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_field_attachments_attachable_type_attachable_id_index` (`attachable_type`,`attachable_id`),
  KEY `nova_field_attachments_url_index` (`url`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_notifications` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `nova_pending_field_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nova_pending_field_attachments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `draft_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attachment` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `disk` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `nova_pending_field_attachments_draft_id_index` (`draft_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `oauth_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `oauth_access_tokens` (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `client_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `scopes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `revoked` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_access_tokens_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `oauth_auth_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `oauth_auth_codes` (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `client_id` bigint unsigned NOT NULL,
  `scopes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `revoked` tinyint(1) NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_auth_codes_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `oauth_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `oauth_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `redirect` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `personal_access_client` tinyint(1) NOT NULL,
  `password_client` tinyint(1) NOT NULL,
  `revoked` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_clients_user_id_index` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `oauth_personal_access_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `oauth_personal_access_clients` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `oauth_refresh_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `oauth_refresh_tokens` (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `access_token_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `revoked` tinyint(1) NOT NULL,
  `expires_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `oauth_refresh_tokens_access_token_id_index` (`access_token_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `operations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `operations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `dispatched` enum('sync','async') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `otp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `otp` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `otp_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_resets` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `proofs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `proofs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `employee_id` bigint unsigned NOT NULL,
  `notification_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `location_id` bigint unsigned DEFAULT NULL,
  `lat` double DEFAULT NULL,
  `lng` double DEFAULT NULL,
  `responded_at` timestamp NULL DEFAULT NULL,
  `expire_at` timestamp NULL DEFAULT NULL,
  `score` tinyint NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `proofs_team_id_foreign` (`team_id`),
  KEY `proofs_employee_id_foreign` (`employee_id`),
  KEY `proofs_location_id_foreign` (`location_id`),
  KEY `proofs_notification_id_index` (`notification_id`),
  CONSTRAINT `proofs_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `proofs_location_id_foreign` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`),
  CONSTRAINT `proofs_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `report_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `report_tasks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `status` enum('success','failed','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `data` json DEFAULT NULL,
  `team_id` bigint unsigned NOT NULL,
  `created_by_id` bigint unsigned DEFAULT NULL,
  `report_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `created_by_type` enum('employee','system') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'employee',
  PRIMARY KEY (`id`),
  KEY `report_tasks_team_id_foreign` (`team_id`),
  KEY `report_tasks_created_by_id_foreign` (`created_by_id`),
  KEY `report_tasks_report_id_foreign` (`report_id`),
  CONSTRAINT `report_tasks_created_by_id_foreign` FOREIGN KEY (`created_by_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `report_tasks_report_id_foreign` FOREIGN KEY (`report_id`) REFERENCES `reports` (`id`),
  CONSTRAINT `report_tasks_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reports` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `reports_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `shifts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shifts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Default Shift',
  `working_hours` json NOT NULL,
  `force_checkout` time NOT NULL DEFAULT '00:00:00',
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `timezone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Asia/Riyadh',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `shifts_team_id_foreign` (`team_id`),
  CONSTRAINT `shifts_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `software_features`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `software_features` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `software_package_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `software_features_code_software_package_id_unique` (`code`,`software_package_id`),
  KEY `software_features_software_package_id_foreign` (`software_package_id`),
  CONSTRAINT `software_features_software_package_id_foreign` FOREIGN KEY (`software_package_id`) REFERENCES `software_packages` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `software_packages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `software_packages` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `software_packages_code_unique` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `subscription_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subscription_items` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `team_id` bigint unsigned NOT NULL,
  `subscription_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `software_package_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscription_items_team_id_unique` (`team_id`),
  UNIQUE KEY `subscription_items_subscription_id_unique` (`subscription_id`),
  KEY `subscription_items_software_package_id_foreign` (`software_package_id`),
  CONSTRAINT `subscription_items_software_package_id_foreign` FOREIGN KEY (`software_package_id`) REFERENCES `software_packages` (`id`),
  CONSTRAINT `subscription_items_subscription_id_foreign` FOREIGN KEY (`subscription_id`) REFERENCES `subscriptions` (`id`),
  CONSTRAINT `subscription_items_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subscriptions` (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `team_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `subscriptions_team_id_unique` (`team_id`),
  CONSTRAINT `subscriptions_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `taggables`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `taggables` (
  `tag_id` bigint unsigned NOT NULL,
  `taggable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `taggable_id` bigint unsigned NOT NULL,
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id`),
  UNIQUE KEY `taggables_tag_id_taggable_id_taggable_type_unique` (`tag_id`,`taggable_id`,`taggable_type`),
  KEY `taggables_taggable_type_taggable_id_index` (`taggable_type`,`taggable_id`),
  CONSTRAINT `taggables_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `team_id` bigint unsigned NOT NULL,
  `color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tags_team_id_name_unique` (`team_id`,`name`),
  CONSTRAINT `tags_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `teams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `teams` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `remote_work` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `free_checkout` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `primary_color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#777777',
  `employees_weekly_summary` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `approval_requests_limit` bigint unsigned NOT NULL DEFAULT '5',
  `leave_request` tinyint(1) NOT NULL DEFAULT '1',
  `approval_request` tinyint(1) NOT NULL DEFAULT '1',
  `permission_request` tinyint(1) NOT NULL DEFAULT '1',
  `permission_request_daily_limit_hours` int unsigned DEFAULT '3',
  `permission_request_monthly_limit_hours` int unsigned DEFAULT '12',
  `remote_work_days_monthly_limit` int unsigned DEFAULT NULL,
  `remote_work_days_weekly_limit` int unsigned DEFAULT NULL,
  `remote_work_days_yearly_limit` int unsigned DEFAULT NULL,
  `vacation_weekend` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'this field indicate whether a tenant consider weekend as part of employee vacation or not',
  `nawart_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '(DC2Type:guid)',
  `checkout_reminder_config` enum('by_shift_end','by_checkin','by_flexible_hours_end') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'by_shift_end',
  `map_report_thresholds` json DEFAULT NULL,
  `colored_logo_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `white_logo_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `early_late_config` json NOT NULL,
  `random_proof_notification_config` json NOT NULL,
  `approval_type` enum('ONE_LAYER','TWO_LAYER') COLLATE utf8mb4_unicode_ci NOT NULL,
  `employee_statement_config` json NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `teams_nawart_uuid_unique` (`nawart_uuid`),
  KEY `teams_nawart_uuid_index` (`nawart_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `webhook_job_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhook_job_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `webhook_job_id` bigint unsigned NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `http_code` smallint unsigned DEFAULT NULL,
  `response` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `webhook_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhook_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `webhook_id` bigint unsigned NOT NULL,
  `tries` smallint NOT NULL DEFAULT '0',
  `event_payload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','SUCCESS','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `webhooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhooks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `secret` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `active` tinyint(1) NOT NULL,
  `event_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `webhooks_team_id_foreign` (`team_id`),
  CONSTRAINT `webhooks_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `workdays`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workdays` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint unsigned NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `color` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `flexible_time_before` time NOT NULL,
  `flexible_time_after` time NOT NULL,
  `prevent_checkout_after` time NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `workflow_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `job` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `workflow_id` bigint unsigned NOT NULL,
  `edges` json DEFAULT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci,
  `finished_at` timestamp NULL DEFAULT NULL,
  `failed_at` timestamp NULL DEFAULT NULL,
  `gated_at` timestamp NULL DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT NULL,
  `gated` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `workflow_jobs_workflow_id_foreign` (`workflow_id`),
  KEY `workflow_jobs_uuid_index` (`uuid`),
  CONSTRAINT `workflow_jobs_workflow_id_foreign` FOREIGN KEY (`workflow_id`) REFERENCES `workflows` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `workflows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflows` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `job_count` int NOT NULL,
  `jobs_processed` int NOT NULL,
  `jobs_failed` int NOT NULL,
  `finished_jobs` json NOT NULL,
  `workflowable_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `workflowable_id` bigint unsigned DEFAULT NULL,
  `then_callback` text COLLATE utf8mb4_unicode_ci,
  `finished_at` timestamp NULL DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `catch_callback` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `workflows_workflowable_type_workflowable_id_index` (`workflowable_type`,`workflowable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!50001 DROP VIEW IF EXISTS `employee_managers_view`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`db`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `employee_managers_view` AS select `e`.`id` AS `employee_id`,`e`.`team_id` AS `employee_team_id`,coalesce(`e`.`manager_id`,`d`.`manager_id`) AS `current_manager_id` from (`employees` `e` left join `departments` `d` on(((`e`.`department_id` = `d`.`id`) and (`e`.`team_id` = `d`.`team_id`)))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_100000_create_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2018_01_01_000000_create_action_events_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2019_05_10_000000_add_fields_to_action_events_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2021_08_25_193039_create_nova_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2022_01_16_102930_create_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2022_01_16_102932_create_shifts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2022_01_16_102935_create_departments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2022_01_16_102936_create_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2022_01_16_102937_create_webhooks_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2022_01_16_102939_create_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2022_01_16_102941_create_activities_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2022_01_16_102942_create_attendances_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2022_01_17_180121_add_current_team_id_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2022_01_25_145458_create_invitations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2022_02_04_224142_create_otp_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2022_04_26_000000_add_fields_to_nova_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2022_06_07_131858_add_manager_to_departments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2022_06_12_162729_create_requests_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2022_07_24_154411_create_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2022_07_25_233744_create_proofs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2022_11_24_160445_create_webhook_logs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2022_12_19_000000_create_field_attachments_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2023_02_28_000000_create_one_time_operations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2023_03_14_152655_create_import_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2023_03_14_152705_create_import_data_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2023_05_02_134228_add_approval_requests_number_to_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2023_05_11_103112_create_leaves_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2023_05_16_142043_create_webhook_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2023_05_16_142937_create_webhook_job_logs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2023_06_07_000001_create_pulse_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2023_06_19_160916_add__random_notifications_policy',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2023_06_20_163049_add_random_notifications_to_attendances',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2023_07_03_123903_add_is_admin_to_users',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2023_07_05_141302_add_employees_weekly_summary_to_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2023_07_13_133836_additnal_team_policies',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2023_07_24_130107_add_email_sent_at_to_invitations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2023_08_07_135211_add_is_adjusted_to_attendances_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2023_08_20_144502_add_flexible_hours_to_attendance',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2023_09_04_151418_add_unique_for_day_month_year_employee_id_to_attendances_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2023_09_19_122302_add_employee_number_to_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2023_10_04_130709_add_soft_delete_to_departments',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2023_10_18_091034_create_locationables_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2023_11_06_143150_add_employee_number_to_invitation_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2023_12_13_125721_add_is_active_to_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2023_12_14_100357_add_remote_work_days_monthly_limit_to_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2023_12_17_113809_add_attendance_type_to_approval_requests_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2023_12_24_115421_add_expires_at_to_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2024_01_01_152159_add_periodical_early_late_report_to_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2024_01_04_140332_add_forgin_key_of_first_checkin_location_and_last_checkout_location_to_attendances_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2024_01_11_114918_add_have_location_to_attendances_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2024_01_15_090940_create_holidays_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2024_01_15_141522_add_dates_to_shifts',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2024_01_15_153327_create_employee_shift_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2024_01_25_140742_add_shift_fields_to_invitations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2024_01_28_095822_add_vacation_weekend_field_to_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2024_01_28_120348_make_shift_id_nullable_in_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2024_02_08_122315_add_start_at_and_end_at_to_employee_shift_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2024_02_08_122315_drop_start_at_and_end_at_from_shifts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2024_02_11_134303_drop_unique_employee_id_and_shift_id_from_employee_shift_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2024_02_14_134213_add_nawart_uuid_to_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2024_02_15_150722_set_email_nullable_in_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2024_02_15_161622_add_email_to_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2024_03_06_155027_set_user_id_nullable_in_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2024_03_29_232544_set_email_as_unique_with_team_id_in_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2024_04_16_134322_create_admins_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2024_04_25_080905_change_nawart_uuid_to_not_nullable',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2024_04_25_114644_create_audits_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2024_04_28_121152_set_team_id_nullable_in_audits_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2024_04_29_083605_add_date_to_attendances_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2024_04_29_111100_add_random_proof_of_attendance_count_configuration_in_teams_and_dept_and_employee_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2024_04_30_103932_change_nawart_uuid_to_unique',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2024_05_06_112329_add_random_proof_of_attendance_deadline_policy_in_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2024_05_19_124732_add_is_default_boolean_field_in_locations_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2024_07_11_094843_add_last_activity_at_to_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2024_07_11_124708_add_first_login_at_in_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2024_07_16_105502_drop_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2024_07_16_111418_fix_timezone_columns',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2024_07_21_125954_create_tag_tables',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2024_07_25_132559_add_nested_periodical_report_policy_field_to_teams',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2024_08_01_131503_add_is_default_to_shifts_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2024_08_05_055929_add_early_late_periodical_report_sheet_mode_to_teams',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2024_08_05_065134_add_checkout_reminder_config_to_teams_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2024_08_05_095412_add_manager_id_to_employees_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2024_08_06_055506_create_employee_managers_view',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2024_08_07_092611_mark_department_id_as_nullable_in_leaves_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2024_08_10_142548_add_is_checked_attribute_to_leaves_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2024_08_11_063204_mark-position-nullable-in-employee-table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2024_08_11_063204_mark_position_nullable_in_employee_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2024_08_20_112111_add_map_report_color_threshold_field_in_teams_table',2);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2024_08_21_122356_mark_mobile_nullable_in_employees_table',3);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (95,'2024_08_22_094518_populate_map_report_threshold_in_teams_table',4);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (96,'2016_06_01_000001_create_oauth_auth_codes_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (97,'2016_06_01_000002_create_oauth_access_tokens_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (98,'2016_06_01_000003_create_oauth_refresh_tokens_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (99,'2016_06_01_000004_create_oauth_clients_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (100,'2016_06_01_000005_create_oauth_personal_access_clients_table',5);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (101,'2024_08_27_123011_mark-last-name-as-nullable-in-employees-table',6);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (107,'2024_09_05_064534_mark_department_id_nullable_in_approval_requests_table',7);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (120,'2024_09_05_090056_create_software_packages_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (121,'2024_09_05_104955_create_subscriptions_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (122,'2024_09_05_105456_create_subscription_items_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (123,'2024_09_05_130736_create_software_features_table',8);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (124,'2024_09_08_140458_change_auditable_id_in_audits_table',9);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (125,'2024_09_15_115634_create_delegationable_table',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (126,'2024_09_30_110950_drop_soft_deletes_in_departments_table',10);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (127,'2024_10_08_131950_add_columns_to_employees_table',11);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (128,'2024_10_22_081220_add_synced_to_attendance_at_to_leaves_table',12);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (129,'2024_10_07_110438_add_third_option_to_checkout_reminder_in_teams_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (130,'2024_10_21_122254_create_devices_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (131,'2024_10_29_104255_create_report_excluded_tags_table',13);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (132,'2024_11_03_120819_drop_pulse_tables',14);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (133,'2024_11_04_080441_add_roles_to_employees_table',15);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (167,'2024_11_04_103408_add_auto_checkin_related_field_in_locations_table',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (168,'2024_11_05_073751_create_reports_table',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (169,'2024_11_05_083126_create_report_tasks_table',16);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (170,'2024_11_13_084712_add_logos_to_teams_table',17);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (171,'2024_11_13_114626_drop_logo_column_in_teams_table',18);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (172,'2024_11_24_132242_add_logos_urls_to_teams_table',19);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (173,'2024_12_03_090323_drop_package_from_teams_table',20);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (174,'2024_12_15_075343_add_lat_lng_to_devices_table',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (175,'2024_12_15_075505_add_device_id_to_activities_table',21);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (179,'2024_12_17_113130_add_early_late_config_to_teams_table',22);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (180,'2024_12_15_145527_add_daily_and_monthly_limits_for_permission_requests_in_teams_table',23);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (181,'2024_12_22_155023_drop_old_columns',24);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (182,'2024_12_22_145059_add_weekly_and_yearly_limits_for_remote_work_in_teams_table',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (183,'2024_12_25_130949_add_attachment_to_leaves_table',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (184,'2025_01_06_132552_add_random_proof_notification_config_to_tables',25);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (187,'2025_01_09_134629_add_reject_reason_to_leaves_table',26);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (188,'2025_01_13_110515_add_rejection_reason_to_approval_requests_table',26);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (189,'2025_02_03_104222_drop_old_columns_from_tables',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (190,'2025_02_03_104650_mark_config_columns_as_not_null_in_tables',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (191,'2025_02_03_112512_drop_report_excluded_tags_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (192,'2025_02_05_105930_add_payload_field_to_activities',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (193,'2025_02_06_101922_drop_day_month_year_in_attendances_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (194,'2025_02_06_111451_add_back_employee_id_foreign_key_to_attendances_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (195,'2025_02_06_111847_drop_old_columns_from_attendances_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (196,'2025_02_06_113609_drop_day_of_week_from_attendances_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (197,'2025_03_04_170938_add_created_by_type_to_report_tasks_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (198,'2025_03_04_171055_mark_created_by_id_as_nullable_in_report_tasks_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (199,'2025_03_10_141426_add_approval_type_to_teams_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (200,'2025_03_12_152544_add_primary_key_to_employee_shift_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (201,'2025_03_18_151428_create_workflow_tables',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (202,'2025_03_24_070238_add_employee_statement_config_to_teams_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (203,'2025_03_24_075550_add_has_employee_statement_to_attendances_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (204,'2025_03_24_102014_create_employee_statements_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (205,'2025_04_09_120618_add_requestable_to_employee_statements_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (206,'2025_04_09_145134_add_device_id_to_attendances_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (207,'2025_04_21_115007_add_is_ready_to_employees_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (208,'2025_04_24_121259_create_decisions_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (209,'2025_05_08_144821_add_primary_key_to_taggables_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (210,'2025_05_08_144857_add_primary_key_to_password_resets_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (211,'2025_05_13_113243_create_daily_plans_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (212,'2025_05_18_161204_create_workdays_table',27);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (213,'2025_05_19_120323_add_indexes',27);
