<?php

namespace Database\Factories;

use App\Models\Workday;
use App\Models\WorkdayWorkSchedule;
use App\Models\WorkSchedule;
use Illuminate\Database\Eloquent\Factories\Factory;

class WorkdayWorkScheduleFactory extends Factory
{
    protected $model = WorkdayWorkSchedule::class;

    public function definition(): array
    {
        return [
            'workday_id' => Workday::factory(),
            'work_schedule_id' => WorkSchedule::factory(),
            'work_days_number' => fake()->numberBetween(1, 7),
            'off_days_number' => fake()->numberBetween(0, 3),
            'repetitions_number' => fake()->numberBetween(1, 12),
        ];
    }
}
