<?php

namespace Database\Factories;

use App\DTOs\EarlyLateConfig;
use App\DTOs\EmployeeStatementConfig;
use App\DTOs\RandomProofNotificationConfig;
use App\Enums\ApprovalType;
use App\Enums\CheckoutReminderConfig;
use App\Enums\EarlyLateNestingPolicy;
use App\Enums\EarlyLatePeriodPolicy;
use App\Enums\RandomProofOfAttendanceDeadline;
use App\Enums\RemoteWorkPolicy;
use App\Enums\SheetMode;
use App\Enums\SoftwarePackageCode;
use App\Models\SoftwarePackage;
use App\Models\Subscription;
use App\Models\SubscriptionItem;
use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\Factory;

class TeamFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'active' => true,
            'email' => fake()->email(),
            'primary_color' => fake()->hexColor(),
            'remote_work' => fake()->randomElement(array_pluck(RemoteWorkPolicy::cases(), 'value')),
            'free_checkout' => 'not_allowed',
            'approval_request' => fake()->boolean(),
            'approval_requests_limit' => 5,
            'remote_work_days_yearly_limit' => null,
            'remote_work_days_monthly_limit' => (int) fake()->randomFloat(0, 0, 31),
            'remote_work_days_weekly_limit' => null,
            'leave_request' => fake()->boolean(),
            'permission_request' => fake()->boolean(),
            'vacation_weekend' => fake()->boolean(),
            'employees_weekly_summary' => fake()->boolean(),
            'checkout_reminder_config' => fake()->randomElement(
                array_pluck(CheckoutReminderConfig::cases(), 'value')
            ),
            'nawart_uuid' => fake()->uuid(),
            'map_report_thresholds' => [
                'yellow' => [
                    'min' => fake()->numberBetween(60, 75),
                    'max' => fake()->numberBetween(75, 90),
                ],
            ],
            'early_late_config' => new EarlyLateConfig(
                enabled: fake()->boolean(),
                periodPolicy: fake()->randomElement(EarlyLatePeriodPolicy::class),
                nestingPolicy: fake()->randomElement(EarlyLateNestingPolicy::class),
                sheetMode: fake()->randomElement(SheetMode::class),
                excludedTags: []
            ),
            'random_proof_notification_config' => new RandomProofNotificationConfig(
                enabled: fake()->boolean(),
                inherited: false,
                count: 5,
                deadline: RandomProofOfAttendanceDeadline::fifteen
            ),
            'employee_statement_config' => new EmployeeStatementConfig(),
            'permission_request_monthly_limit_hours' => null,
            'permission_request_daily_limit_hours' => null,
            'approval_type' => ApprovalType::OneLayer->value,
            'new_work_schedule_feature_enabled' => false,
        ];
    }

    public function active(): static
    {
        return $this->state(fn() => ['active' => true]);
    }

    public function inactive(): static
    {
        return $this->state(fn() => ['active' => false]);
    }

    public function enterprise(): static
    {
        return $this->afterCreating(function (Team $team) {
            $softwarePackage = SoftwarePackage::where(
                'code',
                SoftwarePackageCode::Enterprise1
            )->firstOrFail();

            $subscription = Subscription::factory()->for($team)->create();

            SubscriptionItem::factory()
                ->for($subscription)
                ->for($softwarePackage)
                ->for($team)
                ->create();
        });
    }

    public function basic(): static
    {
        return $this->afterCreating(function (Team $team) {
            $softwarePackage = SoftwarePackage::where(
                'code',
                SoftwarePackageCode::Basic1
            )->firstOrFail();

            $subscription = Subscription::factory()->for($team)->create();

            SubscriptionItem::factory()
                ->for($subscription)
                ->for($softwarePackage)
                ->for($team)
                ->create();
        });
    }

    public function earlyLateConfig(
        bool $enabled = false,
        EarlyLatePeriodPolicy $periodPolicy = null,
        EarlyLateNestingPolicy $nestingPolicy = null,
        SheetMode $sheetMode = null,
        array $excludedTags = []
    ): static {
        return $this->state(
            fn(array $attributes) => [
                'early_late_config' => new EarlyLateConfig(
                    enabled: $enabled,
                    periodPolicy: $periodPolicy ??
                        fake()->randomElement(EarlyLatePeriodPolicy::class),
                    nestingPolicy: $nestingPolicy ??
                        fake()->randomElement(EarlyLateNestingPolicy::class),
                    sheetMode: $sheetMode ?? fake()->randomElement(SheetMode::class),
                    excludedTags: $excludedTags
                ),
            ]
        );
    }

    public function randomProofNotificationConfig(
        bool $enabled = false,
        int $count = 5,
        RandomProofOfAttendanceDeadline $deadline = null
    ): static {
        return $this->state(
            fn(array $attributes) => [
                'random_proof_notification_config' => new RandomProofNotificationConfig(
                    enabled: $enabled,
                    inherited: false,
                    count: $count,
                    deadline: $deadline ?? RandomProofOfAttendanceDeadline::fifteen
                ),
            ]
        );
    }
}
