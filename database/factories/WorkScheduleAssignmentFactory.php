<?php

namespace Database\Factories;

use App\Enums\WorkScheduleAssignmentType;
use App\Models\Team;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleAssignment;
use Illuminate\Database\Eloquent\Factories\Factory;

class WorkScheduleAssignmentFactory extends Factory
{
    protected $model = WorkScheduleAssignment::class;

    public function definition(): array
    {
        return [
            'team_id' => Team::factory(),
            'work_schedule_id' => WorkSchedule::factory(),
            'type' => fake()->randomElement(WorkScheduleAssignmentType::cases()),
            'value' => [1, 2, 3],
        ];
    }

    public function employee(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleAssignmentType::Employee,
            ]
        );
    }

    public function department(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleAssignmentType::Department,
            ]
        );
    }

    public function directManager(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleAssignmentType::DirectManager,
            ]
        );
    }

    public function tag(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleAssignmentType::Tag,
            ]
        );
    }

    public function location(): static
    {
        return $this->state(
            fn(array $attributes) => [
                'type' => WorkScheduleAssignmentType::Location,
            ]
        );
    }
}
