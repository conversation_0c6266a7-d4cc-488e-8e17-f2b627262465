<?php

namespace Database\Seeders;

use App\Models\Activity;
use App\Models\Attendance;
use App\Models\Team;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class AttendanceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (config('app.env') !== 'local') {
            $this->command->error('This seeder should not be run in a production env');

            return;
        }

        $teamId = config('app.local_seeder_team_id');

        $team = Team::withoutGlobalScopes()->with('employees')->find($teamId);
        foreach ($team->employees as $employee) {
            $working_hours = $employee->shift->working_hours;
            for ($i = 0; $i < 100; $i++) {
                $date = Carbon::now()->subDays($i);

                if (
                    Attendance::where('employee_id', $employee->id)
                        ->date($date)
                        ->first()
                ) {
                    continue;
                }

                $today_shift = $working_hours['weekdays'][strtolower($date->format('l'))];
                $today_in = $today_shift ? $today_shift['from'] : null;
                $today_out = $today_shift ? $today_shift['to'] : null;
                $is_weekday = $today_in;
                $check_in = Carbon::parse('00:00');
                $check_out = Carbon::parse('00:00');
                if ($is_weekday) {
                    $shift_from = Carbon::parse($today_in);
                    $shift_to = Carbon::parse($today_out);
                    $is_weekend = false;

                    $random = random_int(1, 10);

                    if ($random <= 2) {
                        $status = Attendance::ABSENT;
                        $in_type = null;
                        $out_type = null;
                    } else {
                        $status = Attendance::PRESENT;
                        $in_type = 'CHECK_IN';
                        $out_type = Activity::FORCE_CHECK_OUT;
                        $check_in = $shift_from->copy()->addMinutes(random_int(1, 45));
                        $check_out = $shift_to->copy()->addMinutes(random_int(1, 45));
                    }
                } else {
                    $shift_from = Carbon::parse('00:00');
                    $shift_to = Carbon::parse('00:00');
                    $is_weekend = true;
                }

                $start = Carbon::today();
                $diff = $check_in->diffInSeconds($check_out);
                //Activity::FORCE_CHECK_OUT

                Attendance::create([
                    'team_id' => $employee->team_id,
                    'employee_id' => $employee->id,
                    'shift_id' => $employee->shift_id,
                    'check_in' => $check_in,
                    'check_out' => $check_out,
                    'shift_from' => $shift_from,
                    'shift_to' => $shift_to,
                    'net_hours' => $start->addSeconds($diff),
                    'status' => $is_weekend ? Attendance::WEEKEND : $status,
                    'in_type' => $in_type,
                    'out_type' => $out_type,
                    'is_weekend' => $is_weekend,
                    'created_at' => $date,
                    'updated_at' => $date,
                ]);

                // Attendance::factory()->create(['employee_id' => 4, 'shift_id' => 1, 'created_at' => Carbon::now()->subDays($i), 'team_id' => 3]);
            }
        }
    }
}
