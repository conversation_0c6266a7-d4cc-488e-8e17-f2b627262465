<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('workday_work_schedule', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workday_id');
            $table->foreignId('work_schedule_id');

            // fixed
            $table->json('selected_specific_days')->nullable(); // for example ['monday', 'wednesday', 'friday']

            // rotational
            $table->unsignedTinyInteger('repetitions_number')->nullable();

            // fixed and rotational
            $table->unsignedTinyInteger('work_days_number')->nullable();
            $table->unsignedTinyInteger('off_days_number')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('workday_work_schedule');
    }
};
