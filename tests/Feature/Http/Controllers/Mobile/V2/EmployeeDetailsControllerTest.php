<?php

use App\Enums\RemoteWorkPolicy;
use App\Models\Location;

test('employee can view his details - has manager', function () {
    $manager = createDefaultEmployee();
    $employee = createDefaultEmployee(['manager_id' => $manager->id]);

    $pivot = ['permanent' => false, 'start_date' => now()->subDay(), 'end_date' => now()->addDay()];

    $employee->locations()->attach(
        Location::factory()
            ->for($this->tenant)
            ->create(),
        ['permanent' => true]
    );

    $employee->locations()->attach(
        Location::factory()
            ->for($this->tenant)
            ->create(),
        $pivot
    );

    /** @see \App\Http\Controllers\Mobile\V2\EmployeeDetailsController */
    $response = $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/employees/current')
        ->assertSuccessful();

    $data = $response->json('data');

    expect($data['id'])->toBe($employee->id);
    expect($data['manager']['id'])->toBe($manager->id);
    expect($data['is_manager'])->toBeFalse();

    $locations = $employee->locations;

    $locationsData = $data['active_locations'];

    expect($locationsData[0]['id'])->toBe($locations[0]->id);
    expect($locationsData[0]['permanent'])->toBe(1);

    expect($locationsData[1]['id'])->toBe($locations[1]->id);
    expect($locationsData[1]['permanent'])->toBe(0);
    expect($locationsData[1]['start_date'])->toBe($pivot['start_date']->format('Y-m-d H:i:s'));
    expect($locationsData[1]['end_date'])->toBe($pivot['end_date']->format('Y-m-d H:i:s'));
});

test('employee can view his details - has no manager', function () {
    $employee = createDefaultEmployee();

    $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/employees/current')
        ->assertSuccessful();
});

test('manager can view his details - has manager', function () {
    $manager = createDefaultEmployee();
    createDefaultEmployee(['manager_id' => $manager->id]);

    /** @see \App\Http\Controllers\Mobile\V2\EmployeeDetailsController */
    $response = $this->jwtActingAsMobile($manager)
        ->getJson('api/v2/mobile/employees/current')
        ->assertSuccessful();

    $data = $response->json('data');

    expect($data['id'])->toBe($manager->id);
    expect($data['is_manager'])->toBeTrue();
});

test('employee can view his details - remote work is inherited', function () {
    $employee = createDefaultEmployee();

    $employee->updateOrFail(['remote_work' => 'inherited']);

    $employee->department->updateOrFail(['remote_work' => 'inherited']);

    $employee->team->updateOrFail(['remote_work' => RemoteWorkPolicy::ALLOWED->value]);

    $this->jwtActingAsMobile($employee)
        ->getJson('api/v2/mobile/employees/current')
        ->assertSuccessful()
        ->assertJsonPath('data.remote_work', RemoteWorkPolicy::ALLOWED->value);
});
