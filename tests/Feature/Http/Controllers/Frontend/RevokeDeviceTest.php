<?php

use App\Models\Employee;

test('revoke device successfully', function () {
    $employee = Employee::factory()
        ->for($this->tenant)
        ->create(['device_id' => 'ios']);

    $this->jwtActingAsAttendanceHR($employee)
        ->put<PERSON>son("api/v1/frontend/employees/$employee->id/revoke-device")
        ->assertOk();

    $employee->refresh();

    expect($employee->device_id)->toBeNull();
    expect($employee->device_name)->toBeNull();
    expect($employee->device_os)->toBeNull();
});
