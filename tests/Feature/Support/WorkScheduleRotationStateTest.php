<?php

use App\Models\Team;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Support\WorkScheduleRotationState;

beforeEach(function () {
    $this->tenant = Team::factory()->create();

    $this->workday1 = Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Day Shift']);

    $this->workday2 = Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Night Shift']);

    $this->workday3 = Workday::factory()
        ->for($this->tenant)
        ->create(['name' => 'Weekend Shift']);
});

// Tests for advanceSpecificDaysRotation method
test('advanceSpecificDaysRotation rotates through workdays for rotational schedule', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Initial state should be first workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);

    // First advance - should move to second workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);

    // Second advance - should move to third workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday3);

    // Third advance - should wrap around to first workday
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);

    // Fourth advance - should move to second workday again
    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
});

test('advanceSpecificDaysRotation does not rotate for fixed schedule', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Initial state should be first workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);

    // Multiple advances should stay on the same workday for fixed schedule
    for ($i = 0; $i < 10; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    }
});

test('advanceSpecificDaysRotation handles single workday correctly', function () {
    $workdays = collect([$this->workday1]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Initial state should be the only workday
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);

    // Multiple advances should stay on the same workday (no other workdays to rotate to)
    for ($i = 0; $i < 5; $i++) {
        $rotationState->advanceDay($workSchedule);
        expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
    }
});

test('advanceSpecificDaysRotation handles two workdays correctly', function () {
    $workdays = collect([$this->workday1, $this->workday2]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Test alternating pattern
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);

    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);

    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);

    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);
});

test('advanceSpecificDaysRotation is not affected by off_days_after_each_repetition', function () {
    $workdays = collect([$this->workday1, $this->workday2]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make([
            'off_days_after_each_repetition' => 2, // This should not affect specific days rotation
        ]);

    $rotationState = new WorkScheduleRotationState($workdays);

    // Should still rotate normally regardless of off_days_after_each_repetition
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);

    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday2);

    $rotationState->advanceDay($workSchedule);
    expect($rotationState->getCurrentWorkday())->toBe($this->workday1);
});

test('getCurrentWorkday returns correct workday at any point in rotation', function () {
    $workdays = collect([$this->workday1, $this->workday2, $this->workday3]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState = new WorkScheduleRotationState($workdays);

    // Test that getCurrentWorkday always returns a valid workday
    for ($i = 0; $i < 20; $i++) {
        $currentWorkday = $rotationState->getCurrentWorkday();
        expect($currentWorkday)->toBeInstanceOf(Workday::class);
        expect($workdays->contains($currentWorkday))->toBeTrue();

        $rotationState->advanceDay($workSchedule);
    }
});

test('rotation state handles empty workdays collection gracefully', function () {
    $workdays = collect([]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    // This should not crash, but getCurrentWorkday will return null
    $rotationState = new WorkScheduleRotationState($workdays);

    // We expect this to return null since there are no workdays
    expect($rotationState->getCurrentWorkday())->toBeNull();
});

test('rotation state maintains consistency across different schedule types', function () {
    $workdays = collect([$this->workday1, $this->workday2]);

    // Test Fixed + SpecificDays
    $fixedSpecificSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->make();

    $rotationState1 = new WorkScheduleRotationState($workdays);

    for ($i = 0; $i < 5; $i++) {
        expect($rotationState1->getCurrentWorkday())->toBe($this->workday1);
        $rotationState1->advanceDay($fixedSpecificSchedule);
    }

    // Test Rotational + SpecificDays
    $rotationalSpecificSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->make();

    $rotationState2 = new WorkScheduleRotationState($workdays);

    expect($rotationState2->getCurrentWorkday())->toBe($this->workday1);
    $rotationState2->advanceDay($rotationalSpecificSchedule);
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday2);
    $rotationState2->advanceDay($rotationalSpecificSchedule);
    expect($rotationState2->getCurrentWorkday())->toBe($this->workday1);
});
