<?php

use App\Enums\RequestStatus;
use App\Enums\WorkdayType;
use App\Jobs\GenerateEmployeeWorkScheduleRecordsJob;
use App\Models\Employee;
use App\Models\Holiday;
use App\Models\Leave;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleRecord;
use Carbon\Carbon;
use function Pest\Laravel\assertDatabaseCount;
use function Pest\Laravel\assertDatabaseHas;

beforeEach(function () {
    $this->employee = Employee::factory()
        ->for($this->tenant)
        ->create();
    $this->workday = Workday::factory()
        ->for($this->tenant)
        ->create();
});

test('generates records for fixed schedule with specific days distribution', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should create records for ALL days in the next 6 months
    $expectedRecords =
        Carbon::parse($workSchedule->start_date)->diffInDays(
            Carbon::parse($workSchedule->start_date)->addMonths(6)
        ) + 1;

    assertDatabaseCount('work_schedule_records', $expectedRecords);

    // Check that weekday records are created correctly for selected days
    $mondayDate = Carbon::parse($workSchedule->start_date)->next('Monday');

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $mondayDate->toDateString(),
        'workday_type' => WorkdayType::Weekday->value,
    ]);

    // Check that weekend records are created for unselected days
    $sundayDate = Carbon::parse($workSchedule->start_date)->next('Sunday');

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $sundayDate->toDateString(),
        'workday_type' => WorkdayType::Weekend->value,
    ]);
});

test('generates records for fixed schedule with number of days distribution', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->numberOfDays()
        ->create([
            'start_date' => now()->addDays(1),
            'off_days_after_each_repetition' => 2,
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'work_days_number' => 5,
        'off_days_number' => 2,
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should create records for the next 6 months (every day)
    $expectedRecords =
        Carbon::parse($workSchedule->start_date)->diffInDays(
            Carbon::parse($workSchedule->start_date)->addMonths(6)
        ) + 1;

    assertDatabaseCount('work_schedule_records', $expectedRecords);
});

test('correctly identifies holiday records', function () {
    $holidayDate = now()->addDays(5);

    Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => $holidayDate,
            'end_date' => $holidayDate,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Check that the holiday is correctly identified
    if (
        in_array(strtolower($holidayDate->format('l')), [
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
        ])
    ) {
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'date' => $holidayDate->toDateString(),
            'workday_type' => WorkdayType::Holiday->value,
        ]);
    }
});

test('correctly identifies leave records', function () {
    // Use a specific Monday to ensure it's a weekday
    $leaveDate = now()->next('Monday');

    Leave::factory()
        ->for($this->tenant)
        ->create([
            'employee_id' => $this->employee->id,
            'from_date' => $leaveDate,
            'to_date' => $leaveDate,
            'status' => RequestStatus::Approved,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Check that the leave is correctly identified (Monday is always in our selected_specific_days)
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'date' => $leaveDate->toDateString(),
        'workday_type' => WorkdayType::Leave->value,
    ]);
});

test('generates records for rotational schedule with multiple workdays', function () {
    $workday1 = Workday::factory()
        ->for($this->tenant)
        ->create();
    $workday2 = Workday::factory()
        ->for($this->tenant)
        ->create();

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->numberOfDays()
        ->create([
            'start_date' => now()->addDays(1),
            'off_days_after_each_repetition' => 1,
        ]);

    $workSchedule->workdays()->attach($workday1->id, [
        'work_days_number' => 3,
        'off_days_number' => 1,
        'repetitions_number' => 2,
    ]);

    $workSchedule->workdays()->attach($workday2->id, [
        'work_days_number' => 2,
        'off_days_number' => 2,
        'repetitions_number' => 1,
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should create records for the next 6 months
    $expectedRecords =
        Carbon::parse($workSchedule->start_date)->diffInDays(
            Carbon::parse($workSchedule->start_date)->addMonths(6)
        ) + 1;

    assertDatabaseCount('work_schedule_records', $expectedRecords);

    // Check that both workdays are used
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $workday1->id,
    ]);

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $workday2->id,
    ]);
});

test('deletes existing future records before generating new ones', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    // Create some existing records with different dates
    for ($i = 10; $i < 15; $i++) {
        WorkScheduleRecord::factory()->create([
            'team_id' => $this->tenant->id,
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $this->workday->id,
            'date' => now()->addDays($i),
        ]);
    }

    $initialCount = WorkScheduleRecord::count();
    expect($initialCount)->toBe(5);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should have deleted the old records and created new ones
    $finalCount = WorkScheduleRecord::count();
    expect($finalCount)->toBeGreaterThan(5); // Should have more records for 6 months
});

test('does not generate records for past dates', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->subDays(10), // Past date
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should start from today, not from the past start_date
    $earliestRecord = WorkScheduleRecord::where('work_schedule_id', $workSchedule->id)
        ->where('employee_id', $this->employee->id)
        ->orderBy('date')
        ->first();

    expect($earliestRecord->date->toDateString())->toBeGreaterThanOrEqual(now()->toDateString());
});

test('handles empty workdays gracefully', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create([
            'start_date' => now()->addDays(1),
        ]);

    // Don't attach any workdays

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should not create any records
    assertDatabaseCount('work_schedule_records', 0);
});

test('generates records for rotational schedule with specific days - single workday', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->create([
            'start_date' => now()->next('Monday'), // Start on a Monday
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Should create records for the next 6 months
    $expectedRecords =
        Carbon::parse($workSchedule->start_date)->diffInDays(
            Carbon::parse($workSchedule->start_date)->addMonths(6)
        ) + 1;

    assertDatabaseCount('work_schedule_records', $expectedRecords);

    // Check that weekday records are created correctly for selected days
    $mondayDate = Carbon::parse($workSchedule->start_date); // Already a Monday

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $mondayDate->toDateString(),
        'workday_type' => WorkdayType::Weekday->value,
    ]);

    // Check that weekend records are created for unselected days (Saturday/Sunday)
    $saturdayDate = $mondayDate->copy()->next('Saturday');

    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $saturdayDate->toDateString(),
        'workday_type' => WorkdayType::Weekend->value,
    ]);
});

test(
    'generates records for rotational schedule with specific days - multiple workdays rotation',
    function () {
        $workday1 = Workday::factory()
            ->for($this->tenant)
            ->create(['name' => 'Morning Shift']);
        $workday2 = Workday::factory()
            ->for($this->tenant)
            ->create(['name' => 'Evening Shift']);

        $workSchedule = WorkSchedule::factory()
            ->for($this->tenant)
            ->rotational()
            ->specificDays()
            ->create([
                'start_date' => now()->next('Monday'),
            ]);

        // First workday: Monday to Wednesday
        $workSchedule->workdays()->attach($workday1->id, [
            'selected_specific_days' => ['monday', 'tuesday', 'wednesday'],
        ]);

        // Second workday: Thursday to Saturday
        $workSchedule->workdays()->attach($workday2->id, [
            'selected_specific_days' => ['thursday', 'friday', 'saturday'],
        ]);

        GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

        $startDate = Carbon::parse($workSchedule->start_date);

        // Day 1 (Monday) should use workday1
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday1->id,
            'date' => $startDate->toDateString(),
            'workday_type' => WorkdayType::Weekday->value, // Monday is in workday1's selected_specific_days
        ]);

        // Day 2 (Tuesday) should use workday2 (rotation advances daily)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday2->id,
            'date' => $startDate->copy()->addDay()->toDateString(),
            'workday_type' => WorkdayType::Weekend->value, // Tuesday is NOT in workday2's selected_specific_days
        ]);

        // Day 3 (Wednesday) should use workday1 again (rotation continues)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday1->id,
            'date' => $startDate->copy()->addDays(2)->toDateString(),
            'workday_type' => WorkdayType::Weekday->value, // Wednesday is in workday1's selected_specific_days
        ]);

        // Verify both workdays are used in the schedule
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday1->id,
        ]);

        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday2->id,
        ]);
    }
);

test('rotational schedule with specific days handles holidays correctly', function () {
    $holidayDate = now()->next('Wednesday'); // Set holiday on a Wednesday

    Holiday::factory()
        ->for($this->tenant)
        ->create([
            'start_date' => $holidayDate,
            'end_date' => $holidayDate,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->create([
            'start_date' => now()->next('Monday'),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Check that the holiday is correctly identified even though Wednesday is in selected_specific_days
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'date' => $holidayDate->toDateString(),
        'workday_type' => WorkdayType::Holiday->value,
    ]);
});

test('rotational schedule with specific days handles leaves correctly', function () {
    $leaveDate = now()->next('Tuesday'); // Set leave on a Tuesday

    Leave::factory()
        ->for($this->tenant)
        ->create([
            'employee_id' => $this->employee->id,
            'from_date' => $leaveDate,
            'to_date' => $leaveDate,
            'status' => RequestStatus::Approved,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->create([
            'start_date' => now()->next('Monday'),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // Check that the leave is correctly identified even though Tuesday is in selected_specific_days
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'date' => $leaveDate->toDateString(),
        'workday_type' => WorkdayType::Leave->value,
    ]);
});

test('rotational schedule with specific days respects vacation_weekend team policy', function () {
    // Set team vacation_weekend to true
    $this->tenant->update(['vacation_weekend' => true]);

    $leaveDate = now()->next('Sunday'); // Set leave on a Sunday (weekend)

    Leave::factory()
        ->for($this->tenant)
        ->create([
            'employee_id' => $this->employee->id,
            'from_date' => $leaveDate,
            'to_date' => $leaveDate,
            'status' => RequestStatus::Approved,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->create([
            'start_date' => now()->next('Monday'),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // With vacation_weekend=true, leave on weekend should be marked as Leave, not Weekend
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'date' => $leaveDate->toDateString(),
        'workday_type' => WorkdayType::Leave->value,
    ]);
});

test('rotational schedule with specific days respects vacation_weekend false policy', function () {
    // Set team vacation_weekend to false
    $this->tenant->update(['vacation_weekend' => false]);

    $leaveDate = now()->next('Sunday'); // Set leave on a Sunday (weekend)

    Leave::factory()
        ->for($this->tenant)
        ->create([
            'employee_id' => $this->employee->id,
            'from_date' => $leaveDate,
            'to_date' => $leaveDate,
            'status' => RequestStatus::Approved,
        ]);

    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->create([
            'start_date' => now()->next('Monday'),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    // With vacation_weekend=false, leave on weekend should be marked as Weekend, not Leave
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'date' => $leaveDate->toDateString(),
        'workday_type' => WorkdayType::Weekend->value,
    ]);
});

test(
    'rotational schedule with specific days - complex rotation pattern over multiple weeks',
    function () {
        $workday1 = Workday::factory()
            ->for($this->tenant)
            ->create(['name' => 'Day Shift']);
        $workday2 = Workday::factory()
            ->for($this->tenant)
            ->create(['name' => 'Night Shift']);
        $workday3 = Workday::factory()
            ->for($this->tenant)
            ->create(['name' => 'Weekend Shift']);

        $workSchedule = WorkSchedule::factory()
            ->for($this->tenant)
            ->rotational()
            ->specificDays()
            ->create([
                'start_date' => now()->next('Monday'),
            ]);

        // Day shift: Monday-Wednesday
        $workSchedule->workdays()->attach($workday1->id, [
            'selected_specific_days' => ['monday', 'tuesday', 'wednesday'],
        ]);

        // Night shift: Thursday-Friday
        $workSchedule->workdays()->attach($workday2->id, [
            'selected_specific_days' => ['thursday', 'friday'],
        ]);

        // Weekend shift: Saturday-Sunday
        $workSchedule->workdays()->attach($workday3->id, [
            'selected_specific_days' => ['saturday', 'sunday'],
        ]);

        GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

        $startDate = Carbon::parse($workSchedule->start_date);

        // Test the first week pattern
        // Day 1 (Monday): workday1 - should be weekday (Monday in selected_specific_days)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday1->id,
            'date' => $startDate->toDateString(),
            'workday_type' => WorkdayType::Weekday->value,
        ]);

        // Day 2 (Tuesday): workday2 - should be weekend (Tuesday NOT in workday2's selected_specific_days)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday2->id,
            'date' => $startDate->copy()->addDay()->toDateString(),
            'workday_type' => WorkdayType::Weekend->value,
        ]);

        // Day 3 (Wednesday): workday3 - should be weekend (Wednesday NOT in workday3's selected_specific_days)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday3->id,
            'date' => $startDate->copy()->addDays(2)->toDateString(),
            'workday_type' => WorkdayType::Weekend->value,
        ]);

        // Day 4 (Thursday): workday1 - should be weekend (Thursday NOT in workday1's selected_specific_days)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday1->id,
            'date' => $startDate->copy()->addDays(3)->toDateString(),
            'workday_type' => WorkdayType::Weekend->value,
        ]);

        // Day 5 (Friday): workday2 - should be weekday (Friday in workday2's selected_specific_days)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday2->id,
            'date' => $startDate->copy()->addDays(4)->toDateString(),
            'workday_type' => WorkdayType::Weekday->value,
        ]);

        // Day 6 (Saturday): workday3 - should be weekday (Saturday in workday3's selected_specific_days)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday3->id,
            'date' => $startDate->copy()->addDays(5)->toDateString(),
            'workday_type' => WorkdayType::Weekday->value,
        ]);

        // Day 7 (Sunday): workday1 - should be weekend (Sunday NOT in workday1's selected_specific_days)
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday1->id,
            'date' => $startDate->copy()->addDays(6)->toDateString(),
            'workday_type' => WorkdayType::Weekend->value,
        ]);

        // Verify all three workdays are used
        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday1->id,
        ]);

        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday2->id,
        ]);

        assertDatabaseHas('work_schedule_records', [
            'work_schedule_id' => $workSchedule->id,
            'employee_id' => $this->employee->id,
            'workday_id' => $workday3->id,
        ]);
    }
);

test('rotational schedule with specific days generates exactly 6 months of records', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->create([
            'start_date' => now()->next('Monday'),
        ]);

    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    $startDate = Carbon::parse($workSchedule->start_date);
    $endDate = $startDate->copy()->addMonths(6);

    // Verify the exact number of records
    $expectedRecords = $startDate->diffInDays($endDate) + 1;
    assertDatabaseCount('work_schedule_records', $expectedRecords);

    // Verify the date range
    $earliestRecord = WorkScheduleRecord::where('work_schedule_id', $workSchedule->id)
        ->where('employee_id', $this->employee->id)
        ->orderBy('date')
        ->first();

    $latestRecord = WorkScheduleRecord::where('work_schedule_id', $workSchedule->id)
        ->where('employee_id', $this->employee->id)
        ->orderBy('date', 'desc')
        ->first();

    expect($earliestRecord->date->toDateString())->toBe($startDate->toDateString());
    expect($latestRecord->date->toDateString())->toBe($endDate->toDateString());
});

test('rotational schedule with specific days handles single workday correctly', function () {
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->rotational()
        ->specificDays()
        ->create([
            'start_date' => now()->next('Monday'),
        ]);

    // Single workday with only weekdays
    $workSchedule->workdays()->attach($this->workday->id, [
        'selected_specific_days' => ['monday', 'wednesday', 'friday'],
    ]);

    GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

    $startDate = Carbon::parse($workSchedule->start_date);

    // For single workday in rotational schedule, it should stay on the same workday
    // (rotation doesn't advance because there's only one workday)

    // Day 1 (Monday): should be weekday
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $startDate->toDateString(),
        'workday_type' => WorkdayType::Weekday->value,
    ]);

    // Day 2 (Tuesday): should be weekend (not in selected_specific_days)
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $startDate->copy()->addDay()->toDateString(),
        'workday_type' => WorkdayType::Weekend->value,
    ]);

    // Day 3 (Wednesday): should be weekday
    assertDatabaseHas('work_schedule_records', [
        'work_schedule_id' => $workSchedule->id,
        'employee_id' => $this->employee->id,
        'workday_id' => $this->workday->id,
        'date' => $startDate->copy()->addDays(2)->toDateString(),
        'workday_type' => WorkdayType::Weekday->value,
    ]);
});

test(
    'rotational schedule with specific days handles empty selected_specific_days array',
    function () {
        $workSchedule = WorkSchedule::factory()
            ->for($this->tenant)
            ->rotational()
            ->specificDays()
            ->create([
                'start_date' => now()->next('Monday'),
            ]);

        // Workday with empty selected_specific_days (all days should be weekends)
        $workSchedule->workdays()->attach($this->workday->id, [
            'selected_specific_days' => [],
        ]);

        GenerateEmployeeWorkScheduleRecordsJob::dispatch($workSchedule, $this->employee);

        $startDate = Carbon::parse($workSchedule->start_date);

        // All days should be marked as weekend since no days are in selected_specific_days
        for ($i = 0; $i < 7; $i++) {
            assertDatabaseHas('work_schedule_records', [
                'work_schedule_id' => $workSchedule->id,
                'employee_id' => $this->employee->id,
                'workday_id' => $this->workday->id,
                'date' => $startDate->copy()->addDays($i)->toDateString(),
                'workday_type' => WorkdayType::Weekend->value,
            ]);
        }
    }
);
