<?php

use App\Jobs\GenerateEmployeeWorkScheduleRecordsJob;
use App\Jobs\GenerateWorkScheduleRecordsJob;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Workday;
use App\Models\WorkSchedule;
use App\Models\WorkScheduleAssignment;
use Illuminate\Support\Facades\Queue;

beforeEach(function () {
    Queue::fake();

    $this->department = Department::factory()
        ->for($this->tenant)
        ->create();

    $this->workday = Workday::factory()
        ->for($this->tenant)
        ->create();
});

test('dispatches individual employee jobs for all resolved employees', function () {
    // Create employees
    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee3 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->inactive()
        ->create();

    // Create work schedule with assignments
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workSchedule->workdays()->attach($this->workday->id);

    // Create assignment for the department
    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->department()
        ->create([
            'work_schedule_id' => $workSchedule->id,
            'value' => [$this->department->id],
        ]);

    // Execute the main job manually (since Queue::fake() prevents actual execution)
    $job = new GenerateWorkScheduleRecordsJob($workSchedule);
    $job->handle();

    // Assert that individual employee jobs were dispatched for active employees only
    Queue::assertPushed(GenerateEmployeeWorkScheduleRecordsJob::class, 2);
});

test('dispatches jobs for employees from multiple assignment types', function () {
    // Create employees
    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $manager = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create(['manager_id' => $manager->id]);

    // Create work schedule
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workSchedule->workdays()->attach($this->workday->id);

    // Create multiple assignments
    $assignment1 = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'work_schedule_id' => $workSchedule->id,
            'value' => [$employee1->id],
        ]);

    $assignment2 = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->directManager()
        ->create([
            'work_schedule_id' => $workSchedule->id,
            'value' => [$manager->id],
        ]);

    // Execute the main job manually (since Queue::fake() prevents actual execution)
    $job = new GenerateWorkScheduleRecordsJob($workSchedule);
    $job->handle();

    // Should dispatch jobs for all unique employees (employee1 and employee2)
    Queue::assertPushed(GenerateEmployeeWorkScheduleRecordsJob::class, 2);
});

test('handles work schedule with no assignments gracefully', function () {
    // Create work schedule without assignments
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workSchedule->workdays()->attach($this->workday->id);

    $job = new GenerateWorkScheduleRecordsJob($workSchedule);
    $job->handle();

    // Should not dispatch any individual employee jobs
    Queue::assertNotPushed(GenerateEmployeeWorkScheduleRecordsJob::class);
});

test('handles work schedule with empty assignment values gracefully', function () {
    // Create work schedule with empty assignment
    $workSchedule = WorkSchedule::factory()
        ->for($this->tenant)
        ->fixed()
        ->specificDays()
        ->create();

    $workSchedule->workdays()->attach($this->workday->id);

    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'work_schedule_id' => $workSchedule->id,
            'value' => [], // Empty values
        ]);

    // Execute the main job manually (since Queue::fake() prevents actual execution)
    $job = new GenerateWorkScheduleRecordsJob($workSchedule);
    $job->handle();

    // Should not dispatch any individual employee jobs
    Queue::assertNotPushed(GenerateEmployeeWorkScheduleRecordsJob::class);
});
