<?php

use App\Models\Department;
use App\Models\Employee;
use App\Models\Location;
use App\Models\Tag;
use App\Models\WorkScheduleAssignment;
use App\Services\ResolveWorkScheduleEmployeesService;

beforeEach(function () {
    $this->department = Department::factory()
        ->for($this->tenant)
        ->create();

    $this->manager = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->create();

    $this->location = Location::factory()
        ->for($this->tenant)
        ->create();

    $this->tag = Tag::factory()
        ->for($this->tenant)
        ->create();
});

test('resolves employees by direct employee assignment', function () {
    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee3 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->inactive()
        ->create();

    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'value' => [$employee1->id, $employee2->id, $employee3->id],
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    // Should return only active employees
    expect($result->count())->toBe(2);
    expect($result->pluck('id')->toArray())->toContain($employee1->id, $employee2->id);
    expect($result->pluck('id')->toArray())->not->toContain($employee3->id);
});

test('resolves employees by department assignment', function () {
    // Create a separate department for testing to avoid including the manager
    $testDepartment = Department::factory()
        ->for($this->tenant)
        ->create();

    $department2 = Department::factory()
        ->for($this->tenant)
        ->create();

    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($testDepartment)
        ->active()
        ->create();

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($testDepartment)
        ->active()
        ->create();

    $employee3 = Employee::factory()
        ->for($this->tenant)
        ->for($department2)
        ->active()
        ->create();

    $employee4 = Employee::factory()
        ->for($this->tenant)
        ->for($testDepartment)
        ->inactive()
        ->create();

    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->department()
        ->create([
            'value' => [$testDepartment->id],
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    // Should return only active employees from the specified department
    expect($result->count())->toBe(2);
    expect($result->pluck('id')->toArray())->toContain($employee1->id, $employee2->id);
    expect($result->pluck('id')->toArray())->not->toContain($employee3->id, $employee4->id);
});

test('resolves employees by direct manager assignment', function () {
    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create(['manager_id' => $this->manager->id]);

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create(['manager_id' => $this->manager->id]);

    $employee3 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create(); // No manager

    $employee4 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->inactive()
        ->create(['manager_id' => $this->manager->id]);

    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->directManager()
        ->create([
            'value' => [$this->manager->id],
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    // Should return only active employees managed by the specified manager
    expect($result->count())->toBe(2);
    expect($result->pluck('id')->toArray())->toContain($employee1->id, $employee2->id);
    expect($result->pluck('id')->toArray())->not->toContain($employee3->id, $employee4->id);
});

test('resolves employees by tag assignment', function () {
    $tag2 = Tag::factory()
        ->for($this->tenant)
        ->create();

    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee3 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee4 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->inactive()
        ->create();

    // Attach tags to employees
    $employee1->tags()->attach($this->tag->id);
    $employee2->tags()->attach($this->tag->id);
    $employee3->tags()->attach($tag2->id);
    $employee4->tags()->attach($this->tag->id);

    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->tag()
        ->create([
            'value' => [$this->tag->id],
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    // Should return only active employees with the specified tag
    expect($result->count())->toBe(2);
    expect($result->pluck('id')->toArray())->toContain($employee1->id, $employee2->id);
    expect($result->pluck('id')->toArray())->not->toContain($employee3->id, $employee4->id);
});

test('resolves employees by location assignment', function () {
    $location2 = Location::factory()
        ->for($this->tenant)
        ->create();

    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee3 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $employee4 = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->inactive()
        ->create();

    // Attach locations to employees with required permanent field
    $employee1->locations()->attach($this->location->id, ['permanent' => true]);
    $employee2->locations()->attach($this->location->id, ['permanent' => true]);
    $employee3->locations()->attach($location2->id, ['permanent' => true]);
    $employee4->locations()->attach($this->location->id, ['permanent' => true]);

    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->location()
        ->create([
            'value' => [$this->location->id],
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    // Should return only active employees assigned to the specified location
    expect($result->count())->toBe(2);
    expect($result->pluck('id')->toArray())->toContain($employee1->id, $employee2->id);
    expect($result->pluck('id')->toArray())->not->toContain($employee3->id, $employee4->id);
});

test('resolves unique employees from multiple assignments', function () {
    // Create a separate department for testing to avoid including the manager
    $testDepartment = Department::factory()
        ->for($this->tenant)
        ->create();

    $employee1 = Employee::factory()
        ->for($this->tenant)
        ->for($testDepartment)
        ->active()
        ->create(['manager_id' => $this->manager->id]);

    $employee2 = Employee::factory()
        ->for($this->tenant)
        ->for($testDepartment)
        ->active()
        ->create();

    // Employee1 is in both assignments (department and direct manager)
    $assignment1 = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->department()
        ->create([
            'value' => [$testDepartment->id],
        ]);

    $assignment2 = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->directManager()
        ->create([
            'value' => [$this->manager->id],
        ]);

    $assignments = collect([$assignment1, $assignment2]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    // Should return unique employees (employee1 should not be duplicated)
    expect($result->count())->toBe(2);
    expect($result->pluck('id')->unique()->count())->toBe(2);
    expect($result->pluck('id')->toArray())->toContain($employee1->id, $employee2->id);
});

test('returns empty collection when no assignments provided', function () {
    $assignments = collect([]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    expect($result->count())->toBe(0);
});

test('returns empty collection when assignment values are empty', function () {
    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'value' => [],
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    expect($result->count())->toBe(0);
});

test('handles non-existent employee IDs gracefully', function () {
    $employee = Employee::factory()
        ->for($this->tenant)
        ->for($this->department)
        ->active()
        ->create();

    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->employee()
        ->create([
            'value' => [$employee->id, 99999], // Non-existent ID
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    // Should return only the existing employee
    expect($result->count())->toBe(1);
    expect($result->first()->id)->toBe($employee->id);
});

test('handles non-existent department IDs gracefully', function () {
    $assignment = WorkScheduleAssignment::factory()
        ->for($this->tenant)
        ->department()
        ->create([
            'value' => [99999], // Non-existent department ID
        ]);

    $assignments = collect([$assignment]);

    $result = ResolveWorkScheduleEmployeesService::handle($assignments);

    expect($result->count())->toBe(0);
});
